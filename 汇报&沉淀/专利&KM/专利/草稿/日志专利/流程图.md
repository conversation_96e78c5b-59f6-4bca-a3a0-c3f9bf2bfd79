                                    【动态知识库技术流程图】
                                           
+--------------------------------+     +-------------------------------------+     +---------------------------------+     +----------------------------------+
|         输入阶段               |     |         知识库构建阶段              |     |         应用阶段                |     |         反馈阶段                |
| (系统输入源)                   | --->| (基于iwiki的动态知识库核心)       | --->| (AI日志定位系统)              | --->| (闭环优化)                    |
|                                |     |                                     |     |                                 |     |                                 |
| ● 用户日志输入                 |     | ★ 场景化知识模板构建模块            |     | ★ 日志预处理模块              |     | ★ 结果输出到终端              |
|   - 原始日志数据 (如自动下载、|     |    -> 使用“>>>”分隔标题/内容        |     |    -> 日志清洗操作:           |     |    - Aisee平台或企微机器人     |
|     弹窗日志)                  |     |    -> 多级嵌套结构 (├─, └─)        |     |        ■ 保留关键行 (e.g.,    |     |    - 提供问题根因分析 (如      |
| ● 业务需求定义                 |     |    -> 字段示例: 用户操作路径、     |     |          过滤tag['DownloadTag'])|     |      "网络超时导致下载失败")   |
|   - 通过Prompt定义分析目标     |     |        系统响应状态码              |     |        ■ 删除噪声行 (e.g.,    |     |                                 |
|     (e.g., 分析下载失败原因)  |     |                                     |     |          删除'type=PLUGIN')    |     |                                 |
|                                |     |                                     |     |        ■ 截取长日志内容        |     |                                 |
|                                |     | ★ 错误码映射机制模块              |     |        ■ 去重重复日志          |     | ★ 分析结果反馈至知识库        |
|                                |     |    -> 键值对映射表 (e.g.,          |     |        ■ JSON转化指定内容      |     |    - 用于知识迭代:             |
|                                |     |        {"retCode=-29": "网络超时"})|     |                                 |     |        ■ 更新模板或映射表      |
|                                |     |    -> 动态更新机制 (iwiki同步)    |     | ★ Prompt生成引擎              |     |        ■ 优化规则集            |
|                                |     |                                     |     |    -> 结合知识库生成指令 (e.g., |     |                                 |
|                                |     | ★ 规则动态注入模块                |     |        "请分析用户操作路径...")|     |                                 |
|                                |     |    -> 日志清洗规则 (正则过滤)      |     |                                 |     |                                 |
|                                |     |    -> Prompt模板 (结构化指令)      |     | ★ 大模型推理模块              |     |                                 |
|                                |     |    -> 秒级更新 (iwiki推送)         |     |    -> 输入清洗后日志和Prompt   |     |                                 |
|                                |     |                                     |     |    -> 输出分析结论 (如轨迹还原)|     |                                 |
|                                |     |                                     |     |                                 |     |                                 |
+--------------------------------+     +-------------------------------------+     +---------------------------------+     +----------------------------------+
                                          ↑                                                   |
                                          |                                                   |
                                          +---------------------------------------------------+
                                                      【闭环优化路径】：分析结果用于知识库迭代