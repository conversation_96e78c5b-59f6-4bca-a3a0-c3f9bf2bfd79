
发明名称: 基于动态模板与大模型协同的终端版本智能发布系统及方法​                       ;

专利交底书正文
1、【关键术语】本发明涉及到的关键术语和定义 
版本智能发布：指通过自动化技术和智能分析，实现软件版本发布的智能化管理和决策。	
灰度实验数据：指在软件版本灰度发布过程中收集的各项质量指标数据，包括崩溃率、ANR率、启动速度、用户活跃度等。
版本需求列表：指某个软件版本包含的所有功能需求的结构化清单，包含需求名称、类型、负责人、测试重点等信息。
2、【发明构思】本发明涉及到的技术关键点或功能关键点介绍
在移动应用快速迭代的开发环境中，版本管理是影响研发效率的关键环节。传统版本管理面临三个核心挑战：一是版本需求信息收集需要在多个平台间手动操作，效率低下且易出错；二是灰度数据分析工作繁重，需要人工处理海量数据并进行复杂的阈值比较；三是版本信息分散在不同系统中，缺乏统一的查询和管理入口；四是灰度数据波动根因分析需要人工下钻决策。
本发明提出了一种基于动态模板与大模型协同的终端版本智能发布系统，通过构建智能化的版本管理流程，实现了从需求收集到发布决策的自动化管理。系统核心创新点包括：
●版本需求收集自动化​：通过接口协同调度机制替代人工跨平台操作，自动聚合需求列表核心字段；
●​灰度报告智能生成​：基于模板自动填充多源监控数据，​融合阈值规则检测与大模型驱动的风险评估结论生成；
●版本信息统一查询：构建跨平台数据枢纽，结合LLM意图解析引擎，​实现企业微信机器人的多角色自助服务与自然语言交互。

3、【背景技术】与本发明最相近的现有技术
3.1相关背景描述，以及现有技术的技术方案
	当前终端版本发布流程主要依赖人工操作或功能有限的工具系统：
	1）人工操作模式​​：在版本需求收集阶段，需在相关平台手动筛选需求单信息，通过复制粘贴方式整合至本地表格；在灰度数据分析过程中，需在数据分析平台查询关键指标，人工修改SQL语句以提取目标数据，并基于预设阈值手动判断指标是否正常。此外，版本计划、覆盖率等核心信息分散在文档系统与监控平台，缺乏统一查询入口。
	2）一站式版本管理工具 ：部分团队虽已开发集成化工具，尝试统一管理版本信息，但其功能局限于版本计划查询、进展状态跟踪等基础性操作，对版本需求仅能提供简陋的信息展示（如需求标题、开发者），​既无法自动化生成包含Crash率、ANR率等关键指标的灰度数据分析报告，也不支持对需求关联数据（如测试关注点、特性实验报告）的深度整合。

3.2现有技术的缺点或尚未解决的问题，以及本专利技术方案可以解决的问题
A、现有技术的缺点或尚未解决的问题
1）人工操作模式效率低下且错误率高  
●跨平台操作效率低下​：需求数据、关键指标及版本信息的收集需在多个独立平台间反复切换操作，流程繁琐且耗时严重；
●数据分析流程可靠性差​​​：人工修改SQL语句提取数据易引入语法错误，且从海量结果中筛选关键指标行时易发生视觉疲劳导致的错漏；
●​​信息碎片化严重​​：版本计划、覆盖率等核心数据分散在文档系统与监控平台，缺乏统一查询入口。
	2）一站式工具功能残缺
●灰度报告自动化能力缺失​：现有工具无法自动生成包含Crash率、ANR率等核心指标的灰度数据分析报告，仍需人工跨平台查询数据并手动整合分析，平均耗时1小时左右；
●数据关联深度不足​：版本需求列表仅支持需求标题、开发者等基础字段的浅层展示，无法关联测试关注点、特性实验报告等关键信息；
●​​智能决策能力空白​​：缺乏对异常数据的阈值自动比对功能及发布风险评估结论生成能力，依赖人工独立完成发布风险判断。
	B、专利技术方案可解决的问题
	本发明通过动态模板与大模型协同技术，构建终端版本智能发布系统，具体解决以下问题：
●多维度需求数据自动化整合：通过接口协同调度引擎自动聚合工蜂、TAPD等平台数据，实现包含需求名称、类型、需求单、工蜂MR链接、产品负责人、终端开发、预埋标记、测试关注点、开关说明、特性实验报告等10项核心字段的版本需求列表自动化生成，消除人工收集的重复操作与数据错漏风险。
●灰度报告全链路智能化：基于动态模板引擎自动调用Bugly、灯塔等接口获取Crash率、ANR率、启动速度等指标数据，通过预设阈值规则执行异常检测（如Crash率超标标识），并联合LLM分析模块生成发布风险评估结论。
●跨角色统一信息中枢：构建企业微信机器人集成入口，实现不同角色（研发/产品等）通过自然语言指令（如“查询指定版本的覆盖率”）自助获取版本信息；研发人员单指令触发需求列表生成、灰度报告导出操作；整合文档系统与监控平台分散数据源。
●知识资产自动化沉淀：建立模板化知识库机制，实现历史需求列表与灰度报告按版本号自动归档，支持版本回溯分析。

4、【发明内容】本专利方案的详细阐述
4.1 产品侧
1）接入企业微信机器人“应用宝终端AI小助手”
●群聊部署​：用户在企业微信任一群聊中进入“添加机器人”界面，搜索“应用宝终端AI小助手”并点击添加按钮，即可完成智能助手的群组集成（见图1）。

![将企业微信机器人添加到群聊](images/image.png)
图1 将企业微信机器人添加到群聊

●私聊部署​：用户通过选中智能助手并点击"发消息"按钮，可激活一对一的私聊交互通道（见图2）。

![私聊企业微信机器人](images/image2.png)
图2 私聊企业微信机器人
	2）版本信息智能查询​：
●用户输入自然语言指令（如"899版本的覆盖率是多少"），智能助手通过LLM解析指令语义，自动调度Beacon等数据接口；
●输出结构化数据（如版本号、活跃用户数、覆盖率百分比）并声明数据来源（见图3）。


![版本信息智能查询交互流程](images/image3.png)
图3 版本信息智能查询交互流程
	
	3）​灰度实验报告自动化生成​：
●用户发送指令（如"获取900版本的灰度实验数据报告"），智能助手触发动态模板引擎；
●自动调用Bugly、灯塔等接口获取数据，填充预置报告模板；
●生成含iwiki持久化链接的分析报告（见图4），支持版本回溯。

![灰度实验报告自动化生成交互流程](images/image4.png)
图4 灰度实验报告自动化生成交互流程

​	4）需求列表自一键整合​：
●响应用户指令（如"查询900版本的需求列表"），通过接口协同引擎聚合工蜂（MR链接）、TAPD（需求单）等平台数据；
●生成包含需求名称、类型、开发者等10项核心字段的需求列表，并通过iwiki文档归档（见图5）。

![需求列表自一键整合交互流程](images/image5.png)
图5 需求列表自一键整合交互流程

4.2技术侧
	1）系统整体技术流程
	如图6所示，是终端版本智能发布系统。用户通过企业微信入口发起自然语言指令，LLM意图解析引擎首先识别指令类型（灰度分析/需求收集/版本查询），随后动态调度相应技术模块。在需求收集场景，系统通过工蜂/TAPD API适配器获取原始数据，经数据清洗模块过滤无效信息后生成表格存储至iwiki知识库；灰度分析场景通过Bugly与Beacon等多源数据融合，结合阈值规则库进行异常检测及LLM智能归因；版本信息查询场景调用Tedi/Bugly聚合器实现动态数据调度。


![系统整体流技术程图](images/image6.png)
图6 系统整体流技术程图

	2）版本需求列表生成技术
	图7展示版本需求收集的模块化技术流程：
●数据获取层 ：Git模块拉取指定时间范围内（上个发布分支创建时间 至今）的合并请求（MR）列表（见图中提取需求范围示意图），通过检查每个MR的改动文件识别非功能性提交（如图中"文件变更-剔除插件"过滤插件更新类提交）；
●数据关联层​：通过MR与TAPD需求单关联的唯一值，找到对应TAPD单，由TAPD模块解析需求类型、产品负责人等字段（图中"TAPD"模块）；
●​数据整合层​：模板引擎将需求名称、工蜂MR链接等10项核心字段分批写入表格，实现从原始提交到结构化文档的自动化转换，支持历史版本回溯分析（见图中版本需求收集流程）。

![版本需求收集流程示意图](images/image7.png)
图7 版本需求收集流程示意图

	3）灰度数据报告智能生成
如图8所示，灰度数据处理采用双引擎驱动架构：
●数据采集引擎​：同步调用Bugly API获取Crash率、ANR率原始值，通过灯塔API抽取启动速度等质量参数；
●​智能决策引擎​：预置阈值规则库（如Crash率阈值=0.18%）自动标红异常数据，触发LLM模块生成发布决策结论；
最终生成含实验配置、用户分层数据和LLM分析结论的结构化报告，经iwiki链接持久化存储。该流程将传统需小时级的人工分析压缩至分级完成，且支持版本回溯对比。


![灰度数据分析流程示意图](images/image8.png)
图8 灰度数据分析流程示意图

	4）版本信息统一查询
基于图9多源数据协同机制，系统实现基于LLM意图识别驱动的四类核心查询能力的深度整合：
●版本流程状态精准获取：通过TEDI API实时调取当前版本关键节点状态，包括代码合流截止时间、灰度发布窗口及全量发布时间；
●版本计划动态同步 ：调用iwiki多维表格API自动提取版本日历事件（如灰度时间）；
●需求分组智能定位 ：基于iwiki多维表格API与用户ID关联机制，准确定位版本需求所在实验分组分支，支持分组策略一键回溯；
●​质量指标结构化输出 ：连接灯塔API解析覆盖率等质量参数（图中"版本覆盖率"模块），输出结构化信息。
上述能力的整合，解决了跨越多平台查询信息的痛点， 实现企业微信机器人的多角色自助服务与自然语言交互。

![版本信息查询架构图](images/image9.png)
图9 版本信息查询架构图

5）LLM意图识别与调度
基于图10时序图，智能交互内核的技术流程严格遵循时间递进原则：用户原始消息首先由业务管理层进行指令分类，随后分发至意图识别层；LLM引擎通过语义理解模型将自然语言指令映射为预定义场景类型（如灰度数据分析、版本信息查询等），输出结构化识别结果至参数解析层，解析输出用户实际意图；Agent执行层依据指令类型调度对应服务模块，并二次调用LLM从用户描述中动态提取关键参数（如版本号），生成API调用序列；数据处理层调用外部接口获取原始数据，经格式化处理后存入对话日志库。全链路支持会话回溯，保障复杂交互的可靠性。

![意图识别交互时序图](images/image10.png)
图10 意图识别交互时序图

4.3 技术方案所产生的有益效果
本专利技术方案通过基于LLM驱动的需求收集自动化、灰度分析智能化与信息枢纽集成化三大核心创新，实现了：
●效率维度​：消除跨平台手动操作，关键流程耗时从小时级压缩至分钟级甚至秒级；
●质量维度​：通过阈值规则与LLM分析替代人工判断，提升决策准确性；
●协作维度​：构建跨角色统一入口，缩短信息流转路径；
●知识维度​：建立可回溯的版本资产库，赋能持续优化。
​	最终达成研发效率、发布质量与团队协同能力的全面升级，为快速迭代环境下的终端版本管理提供标准化、高可靠的解决方案。

