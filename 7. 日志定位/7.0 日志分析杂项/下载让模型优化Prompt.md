你是一个Prompt优化专家，现在要让模型查看日志。现在有 下载日志原文。请帮我写一个Prompt，让模型理解怎么分析下载日志，不要自己创造应用名，不要误判。下载流程要和app名对应

一、现在的Prompt
DOWNLOAD_PROMPT = """

你是一个资深的Android开发工程师，在下载app模块有深入研究，请理解[用户问题]，对提供的[用户下载日志]进行专业分析。请结合[知识库]、[资料]和你的专业知识，严格遵循[要求]，按照[格式]输出：

[操作路径]=\"\"\"
{user_action}
\"\"\"

[知识库]=\"\"\"
{rag}
1.DownloadTag initApkPatchSDK 表示开始下载任务。
2.DownloadInfo是下载信息。包含信息有：包名（packageName），APP名称（name），版本号（versionCode）;
\"\"\"

[资料]=\"\"\"
根据[用户下载日志]，按优先级判断下载结果：
1. 先检查retCode，判断是否下载失败
2. 再检查DownloadInfo中的信息（包名[packageName]，APP名称[name]，版本号[versionCode]）
   例如：downloadApk :DownloadInfo downloadState=INIT, appId=6633, apkId=107368500, downloadTicket=107368500, packageName='com.tencent.mobileqq', name='QQ', versionCode=3350, versionName='8.9.13' 中，
   包名是com.tencent.mobileqq，APP名称是QQ，版本号是3350
3. 最后检查下载结果
\"\"\"

[资料]=\"\"\"
严格从[用户下载日志]分析，分析执行四步法：
① 网络层诊断：
   - 检索所有包含"retCode"的日志条目
   - 建立错误代码→异常类型映射表

② 信息核验：
   - 提取DownloadInfo字段组成元组（包名 name, APP名称 packageName, 版本号 versionCode）
   - 验证元组一致性（同一APP不同阶段的信息必须一致）

③ 行为流分析：
   - 构建下载行为时间轴
   - 校验关键节点存在性（INIT→PROGRESS→SUCC/FAIL）

④ 系统状态比对：
   - 下载完成需验证安装路径包含packageName
   - 取消操作需确认DownloadState为CANCELED且无后续重试
\"\"\"

[用户下载日志]=\"\"\"
{log_content}
\"\"\"

[要求]=\"\"\"
1. 必须采用中文回答。严格参考[资料]执行。
2. 必须给出明确答案是否存在异常。用户取消下载不代表下载存在异常
3. 结合{indexKey}、[知识库]分析业务场景。
4. 严格按照[格式]输出。
5. 如果有异常，请打出关键日志原文。
\"\"\"

[格式]=\"\"\"
# 下载过程是否存在异常:是/否
# 下载结果判断（下载成功/用户取消下载/下载失败/未知状态等等）
# APP信息:下载的APP信息，如APP名称、包名、版本号。（只列出APP的信息，插件（plugin）信息不要列出！）
# 关键证据链（从[用户下载日志]中，找到关键日志原文。按时间顺序输出日志原文）
# 下载流程分析（表格形式输出）
|时间|下载行为|详细分析| 
# 用户下载日志总结
整合分析得出下载日志总结，下载的app详细信息，下载状态，如果下载失败，输出失败的异常分析
\"\"\"
"""

二、分析下载日志的关键点如下：

# 开始下载
如
[YYBWebsocketServer] downloadApk

# DownloadInfo，下载的app信息
如
DownloadTag [YYBWebsocketServer] downloadApk :DownloadInfo{downloadState=INIT, appId=54044876, apkId=129744972, downloadTicket=129744972, packageName='com.wakeup.howear', name='Wearfit Pro', versionCode=2025041414, versionName='zh_5.5.22', scene=2000, statScene=10037, apkUrlList=[https://dd.myapp.com/sjy.00015/16891/apk/F3AB97A74E0606809BC00E54893D5B08.apk?fsname=com.wakeup.howear_zh_5.5.22.apk], autoInstall=true, via='', taskId='', channelId='', actionFlag=0, uiType=NORMAL, fileType=APK, grayVersionCode=0, installType=2, mainUrl=null, isReplaced=false, isSwapped=false, response=DownloadResponse{length=0, totalLength=0, uiPercent=1.0, dSpeed=0.0, speed=0KB/S}, retryDownloadCnt=0, localVersionCode=0, filePath=, downloadingPath=, isUpdate=0, sllUpdate=0, isExternalCall=true, clientfilesize=0, enableTaskDualDownload=false, copyPath=null, shouldSameVersionUpdate=false}


# 取消下载
如
2025-03-18 08:45:52.378 I DownloadTag traceId:0 msg:delDownloadInfo, downloadTicket=125418506  info=DownloadInfo{downloadState=SUCC, appId=12127266, apkId=125418506, downloadTicket=125418506, packageName='com.tencent.tmgp.sgame', name='王者荣耀', versionCode=1001010602, versionName='********', apkUrlList=[http://dd.myapp.com/sjy.00001/808360623AB0A923/16891/apk/3AAE6545984445FA78EABC2D19699A69.apk?fsname=com.tencent.tmgp.sgame_********.apk], autoInstall=false, via='', taskId='', channelId='', actionFlag=0, uiType=WISE_PRE_DOWNLOAD, fileType=APK, grayVersionCode=0, installType=0, mainUrl=null, isReplaced=false, isSwapped=false, response=DownloadResponse{length=0, totalLength=0, uiPercent=1.0, dSpeed=0.0, speed=0KB/S}, retryDownloadCnt=0, localVersionCode=0, filePath=/storage/emulated/0/Android/data/com.tencent.android.qqdownloader/files/tassistant/apk/com.tencent.tmgp.sgame_1001010602.apk, downloadingPath=, isUpdate=0, sllUpdate=0, isExternalCall=false, clientfilesize=0, enableTaskDualDownload=true, copyPath=null, shouldSameVersionUpdate=false}


# 下载成功
如
DownloadTag send download SUCC, pkg=com.tencent.tmgp.sgame


# 下载失败的判断条件之一
retCode，不同的retCode对应不同的原因
如
[halley-downloader-SectionTransport][][1-011B99673D258A33A023EFB76F4C298F:[2|sche] Direct:false send req retCode:-29,msg:java.net.UnknownHostException:Unable to resolve host "beta.myapp.com": No address associated with 





