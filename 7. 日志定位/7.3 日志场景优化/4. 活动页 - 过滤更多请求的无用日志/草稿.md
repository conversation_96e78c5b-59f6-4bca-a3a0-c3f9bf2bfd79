这段 Kotlin 代码定义了一个工具类，主要功能是根据传入的 `ruleId` 返回对应的“规则函数”（`RuleFunc<T>`）。这些规则函数用于从请求响应对象中提取某些特定的状态码或错误码，方便统一处理响应结果。

---

## 代码结构和功能概述

- **包名**：`com.tencent.assistantkuikly.component.moka_data_core.utils`
- **导入的类型**：
  - `ActExecRespBody`：响应体的封装类型，泛型参数为响应体的原始数据类型（这里是 `String`）。
  - `RuleFunc`：函数类型，定义为 `(RuleFuncReq<T>) -> Int`，即接收请求对象，返回一个整数（通常是状态码或错误码）。
  - `RuleFuncReq`：请求对象，包含响应的各种信息，如 `body`、`headers`、`statusCode` 等。

- **核心函数**：
  - `create(ruleId: Int): RuleFunc<T>`：根据 `ruleId` 返回对应的规则函数。
  - `create_106()` ~ `create_119()`：一系列私有函数，分别实现不同的规则函数。

---

## 详细解释

### 1. `create(ruleId: Int): RuleFunc<T>`

这是一个工厂方法，根据传入的 `ruleId` 返回对应的规则函数。规则函数的作用是从请求响应中提取某个字段的值（通常是错误码或状态码），并返回。

```kotlin
fun <T> create(ruleId: Int): RuleFunc<T> {
    if (ruleId == 106) {
        return create_106()
    }
    if (ruleId == 108) {
        return create_108()
    }
    // ... 其他 ruleId 对应的函数
    return { 0 }  // 默认返回一个始终返回0的函数
}
```

---

### 2. 规则函数的定义格式

每个 `create_xxx()` 函数返回一个 `RuleFunc<T>`，即一个函数 `(RuleFuncReq<T>) -> Int`。该函数接收一个请求对象 `req`，从中提取对应的字段，转换成整数返回。

---

### 3. 具体规则函数示例解析

#### `create_108()`

```kotlin
private fun <T> create_108(): RuleFunc<T> {
    return { req: RuleFuncReq<T> ->
        if (req.body != null) {
            val actExecRespBody = req.body as ActExecRespBody<String>
            actExecRespBody.rawBody?.get("errCode")?.toString()?.toIntOrNull() ?: -1
        } else {
            0
        }
    }
}
```

- 作用：从响应体的 `rawBody` 中取出 `"errCode"` 字段，转换为整数返回。
- 如果 `errCode` 不存在或无法转换，返回 `-1`。
- 如果 `body` 为 `null`，返回 `0`。

---

#### `create_109()`

```kotlin
private fun <T> create_109(): RuleFunc<T> {
    return { req: RuleFuncReq<T> ->
        if (req.body != null) {
            val actExecRespBody = req.body as ActExecRespBody<String>
            actExecRespBody.data.rawData?.get("ret")?.toString()?.toIntOrNull() ?: -1
        } else {
            0
        }
    }
}
```

- 作用：从响应体的 `data.rawData` 中取 `"ret"` 字段，转换为整数返回。
- 逻辑同上。

---

#### `create_110()`

```kotlin
private fun <T> create_110(): RuleFunc<T> {
    return { req: RuleFuncReq<T> ->
        if (req.statusCode == 200) 0 else req.statusCode
    }
}
```

- 作用：检查 HTTP 状态码。
- 如果状态码是 200，返回 0（表示成功）。
- 否则返回实际状态码。

---

#### `create_111()`

```kotlin
private fun <T> create_111(): RuleFunc<T> {
    return { req: RuleFuncReq<T> ->
        if (req.headers.containsKey("ual-access-ret")) req.headers["ual-access-ret"]?.toIntOrNull() ?: -1 else -1
    }
}
```

- 作用：从响应头中取 `"ual-access-ret"` 字段，转换为整数返回。
- 不存在或转换失败返回 `-1`。

---

#### `create_115()`

```kotlin
private fun <T> create_115(): RuleFunc<T> {
    return { req: RuleFuncReq<T> ->
        if (req.body != null) {
            val actExecRespBody = req.body as ActExecRespBody<String>
            val ret = actExecRespBody.ret
            if (ret == 0) 0 else -1
        } else {
            -1
        }
    }
}
```

- 作用：判断响应体中的 `ret` 字段是否为 0。
- 是 0 返回 0，表示成功。
- 否则返回 -1，表示失败。

---

#### `create_116()`

```kotlin
private fun <T> create_116(): RuleFunc<T> {
    return { req: RuleFuncReq<T> ->
        val headerRet = req.headers["trpc-func-ret"]?.toIntOrNull() ?: -1
        val actExecRespBody = req.body as ActExecRespBody<String>
        if (headerRet != 0) {
            headerRet
        } else {
            actExecRespBody.ret
        }
    }
}
```

- 作用：先从响应头 `"trpc-func-ret"` 取值，如果不为 0，直接返回该值。
- 否则返回响应体中的 `ret` 字段。

---

#### `create_118()`

```kotlin
private fun <T> create_118(): RuleFunc<T> {
    return { req: RuleFuncReq<T> ->
        if (req.headers.isEmpty() && req.body == null) {
            -1
        } else {
            0
        }
    }
}
```

- 作用：判断响应头和响应体是否都为空。
- 都为空返回 -1，表示无效响应。
- 否则返回 0。

---

### 4. 其他函数

其他 `create_xxx()` 函数逻辑类似，都是从请求对象的不同字段（`headers`、`body`、`statusCode`）中提取特定字段，转换为整数返回。

---

## 总结

这段代码的核心目的是：

- 根据不同的 `ruleId`，返回不同的规则函数。
- 规则函数用于从请求响应对象中提取错误码、状态码或其他标识字段。
- 方便统一处理不同接口或不同业务场景下的响应结果。

这种设计使得业务代码可以通过传入不同的 `ruleId`，灵活地获取对应的状态码提取逻辑，增强了代码的可扩展性和复用性。

---

如果你需要，我可以帮你进一步解释某个具体函数的实现细节或设计思路。