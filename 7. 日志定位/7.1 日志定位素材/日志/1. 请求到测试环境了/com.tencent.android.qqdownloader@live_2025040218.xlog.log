[I][2025-04-02 +80 17:53:14.563][9696, 38][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 17:53:14.664][9696, 38][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MM<PERSON><PERSON><PERSON><PERSON><PERSON> ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 17:53:14.664][9696, 38][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 17:53:14.664][9696, 38][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 17:53:14.664][9696, 38][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 17:53:14.664][9696, 38][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 17:53:14.665][9696, 38][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 17:53:14.665][9696, 38][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 17:53:14.665][9696, 38][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 17:53:14.665][9696, 38][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 17:53:14.665][9696, 38][temporary-2][cache][live][RightlySDKManager][initSDK time =197
[I][2025-04-02 +80 17:53:14.665][9696, 38][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:53:14.665][9696, 38][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 17:53:14.665][9696, 38][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~j--ncEnVQB-7v0RdijlT0g==/com.tencent.android.qqdownloader-Df1heY5iTiy92ZLlG6Fo0A==/base.apk, false
[I][2025-04-02 +80 17:53:14.665][9696, 38][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:53:14.729][9696, 38][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:53:14.729][9696, 38][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~j--ncEnVQB-7v0RdijlT0g==/com.tencent.android.qqdownloader-Df1heY5iTiy92ZLlG6Fo0A==/base.apk
[I][2025-04-02 +80 17:53:14.729][9696, 38][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 17:53:14.729][9696, 38][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~j--ncEnVQB-7v0RdijlT0g==/com.tencent.android.qqdownloader-Df1heY5iTiy92ZLlG6Fo0A==/base.apk
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 17:53:14.730][9696, 38][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][RdefenseInitTask][init
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:53:14.731][9696, 38][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-02 +80 17:53:14.732][9696, 38][temporary-2][live][IoniaStartDaemonProxy][][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 17:53:14.745][9696, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 184, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:53:14.745][9696, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 185, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:53:14.799][9696, 70][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 214, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:53:14.799][9696, 74][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 217, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:53:14.820][9696, 38][temporary-2][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 17:53:14.826][9696, 67][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 221, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 17:53:14.834][9696, 38][temporary-2][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 17:53:14.859][9696, 45][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.864][9696, 45][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.870][9696, 38][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.870][9696, 39][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.892][9696, 42][temporary-6][live][ActivityThreadHacker][][start Hook successful, dur = 421
[I][2025-04-02 +80 17:53:14.907][9696, 40][temporary-4][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 17:53:14.916][9696, 39][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.919][9696, 43][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.921][9696, 41][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.923][9696, 41][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.944][9696, 38][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.964][9696, 41][temporary-5][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:53:14.966][9696, 45][temporary-8][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:53:14.968][9696, 43][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.970][9696, 35][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.973][9696, 35][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:14.978][9696, 39][temporary-3][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:53:14.985][9696, 38][temporary-2][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:53:14.986][9696, 43][temporary-7][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:53:14.987][9696, 35][temporary-1][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:53:15.100][9696, 45][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:15.101][9696, 35][temporary-1][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:15.106][9696, 45][temporary-8][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:53:15.179][9696, 35][temporary-1][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 17:53:15.193][9696, 40][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:15.224][9696, 40][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 17:53:15.262][9696, 40][temporary-4][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 17:53:16.515][9696, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:16.554][9696, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:16.566][9696, 74][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:16.573][9696, 67][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:16.588][9696, 70][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:16.828][9696, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:16.844][9696, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:16.854][9696, 74][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:16.867][9696, 67][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:16.870][9696, 70][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 17:53:19.430][9696, 38][temporary-2][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 17:53:19.666][9696, 39][temporary-3][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 17:53:21.928][9696, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 17:53:23.195][9696, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 17:53:23.198][9696, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 17:53:23.198][9696, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 17:53:23.230][9696, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:53:23.233][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.234][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.235][9696, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 17:53:23.235][9696, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 17:53:23.235][9696, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 17:53:23.236][9696, 45][temporary-8][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 17:53:23.264][9696, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@471b3c0
[I][2025-04-02 +80 17:53:23.265][9696, 108][Binder:9696_8][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 17:53:23.266][9696, 108][Binder:9696_8][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 17:53:23.266][9696, 108][Binder:9696_8][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 17:53:24.703][9696, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 17:57:34.575][9696, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:03:16.716][9696, 70][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:03:16.739][9696, 67][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:03:16.779][9696, 74][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:03:16.786][9696, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:03:16.798][9696, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[11819,12144][2025-04-02 +0800 18:09:15]
get mmap time: 17
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77160579072 available:77026361344
log dir space info, capacity:117409054720 free:77005389824 available:77005389824
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][RightlySDKManager][initSDK time =192
[I][2025-04-02 +80 18:09:15.581][11819, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:15.582][11819, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 18:09:15.583][11819, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 18:09:15.583][11819, 39][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 18:09:15.615][11819, 39][temporary-2][cache][live][RdefenseInitTask][init
[I][2025-04-02 +80 18:09:15.627][11819, 71][io_thread][live][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 18:09:15.615][11819, 39][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-02 +80 18:09:15.925][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 324, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 18:09:16.230][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 628, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 18:09:16.251][11819, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 18:09:16.257][11819, 1*][main][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 18:09:16.278][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 680, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 18:09:16.278][11819, 55][temporary-8][live][BinderManager][][connectToServiceOptimize
[I][2025-04-02 +80 18:09:16.344][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 623, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 18:09:16.397][11819, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 18:09:16.399][11819, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 18:09:16.404][11819, 55][temporary-8][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 18:09:16.407][11819, 55][temporary-8][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 18:09:16.408][11819, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 18:09:16.409][11819, 42][SendEventDispatcher][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 18:09:16.411][11819, 42][SendEventDispatcher][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 18:09:16.416][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 746, dataMap.size = 322, memSize = 325.859375
[I][2025-04-02 +80 18:09:16.429][11819, 44][temporary-6][live][IoniaStartDaemonProxy][][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 18:09:16.435][11819, 44][temporary-6][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[E][2025-04-02 +80 18:09:16.444][11819, 43][temporary-5][live][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 18:09:16.451][11819, 44][temporary-6][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 18:09:16.459][11819, 40][temporary-3][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 18:09:16.478][11819, 55][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.479][11819, 50][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.480][11819, 55][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.481][11819, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.485][11819, 39][temporary-2][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.485][11819, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.487][11819, 50][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[E][2025-04-02 +80 18:09:16.501][11819, 43][temporary-5][live][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 18:09:16.502][11819, 43][temporary-5][live][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 18:09:16.502][11819, 43][temporary-5][live][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 18:09:16.502][11819, 43][temporary-5][live][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 18:09:16.509][11819, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.539][11819, 55][temporary-8][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 18:09:16.540][11819, 39][temporary-2][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 18:09:16.541][11819, 50][temporary-7][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 18:09:16.543][11819, 41][temporary-4][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 18:09:16.560][11819, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.562][11819, 43][temporary-5][live][ActivityThreadHacker][][start Hook successful, dur = 1021
[I][2025-04-02 +80 18:09:16.562][11819, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.590][11819, 40][temporary-3][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 18:09:16.612][11819, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.613][11819, 55][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.616][11819, 50][temporary-7][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 18:09:16.617][11819, 55][temporary-8][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 18:09:16.619][11819, 41][temporary-4][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 18:09:16.645][11819, 50][temporary-7][live][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-02 +80 18:09:17.284][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:17.431][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:17.432][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:17.480][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:17.482][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:17.485][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:17.625][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:17.626][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:17.693][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:17.699][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:09:20.422][11819, 40][temporary-3][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 18:09:20.939][11819, 55][temporary-8][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 18:09:22.973][11819, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 18:09:24.434][11819, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 18:09:24.438][11819, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 18:09:24.438][11819, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 18:09:24.473][11819, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[I][2025-04-02 +80 18:09:24.478][11819, 36][temporary-1][live][MainBinderManager][][tryToConnect process:live
[E][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.478][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 18:09:24.479][11819, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 18:09:24.480][11819, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 18:09:24.517][11819, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@4439ddb
[I][2025-04-02 +80 18:09:24.518][11819, 109][Binder:11819_C][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 18:09:24.519][11819, 109][Binder:11819_C][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 18:09:24.520][11819, 109][Binder:11819_C][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 18:09:25.512][11819, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:12:34.573][11819, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:13:02.223][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:13:02.232][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:13:02.251][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:13:02.279][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:13:02.282][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:19:17.660][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:19:17.668][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:19:17.693][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:19:17.701][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:19:17.701][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:27:34.664][11819, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:29:17.749][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:29:17.765][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:29:17.769][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:29:17.781][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:29:17.782][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:39:17.840][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:39:17.843][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:39:17.880][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:39:17.884][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:39:17.890][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:42:34.652][11819, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 18:49:17.986][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:49:17.987][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:49:17.992][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:49:17.993][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 18:49:18.001][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
