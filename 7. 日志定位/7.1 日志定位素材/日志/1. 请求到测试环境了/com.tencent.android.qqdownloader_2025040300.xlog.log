~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 23:42:46.159][11932, 37][temporary-1][market][RdeliveryConfigFetcher][][canRefreshRDelivery remain:974145
[I][2025-04-02 +80 23:42:46.164][11932, 1*][main][market][CloudDiskAutoBackupManager][][startAllAutoBackupIfNeed
[I][2025-04-02 +80 23:42:46.174][11932, 1*][main][market][CloudDiskAutoBackupManager][][startAlbumBackupIfNeed
[I][2025-04-02 +80 23:42:46.174][11932, 1*][main][market][CloudDiskAutoBackupManager][][checkAllowBackup
[I][2025-04-02 +80 23:42:46.175][11932, 84][timer][market][CloudGameWidgetSolution][][尝试刷新云游退出弹窗Widget失败,因为当前没有对应的Widget, refreshType = timer_job
[I][2025-04-02 +80 23:42:46.176][11932, 84][timer][market][KuiklyPageResourcePreloadTimeJob][][work()
[I][2025-04-02 +80 23:42:46.178][11932, 84][timer][market][KuiklyPageResourcePreloadTimeJob][][period: 10800
[W][2025-04-02 +80 23:42:46.179][11932, 1*][main][market][CloudDiskAutoBackupManager][][checkAllowBackup return: permission
[I][2025-04-02 +80 23:42:46.179][11932, 84][timer][market][BookingPreDown.timer][][work
[I][2025-04-02 +80 23:42:46.180][11932, 1*][main][market][CloudDiskAutoBackupManager][][#startWechatBackupIfNeed
[I][2025-04-02 +80 23:42:46.180][11932, 1*][main][market][CloudDiskAutoBackupManager][][checkAllowBackup
[W][2025-04-02 +80 23:42:46.180][11932, 1*][main][market][CloudDiskAutoBackupManager][][checkAllowBackup return: permission
[I][2025-04-02 +80 23:42:46.180][11932, 84][timer][market][BookingPreDown.Feature][][enableAutoDownloadPatch = true
[I][2025-04-02 +80 23:42:46.180][11932, 84][timer][market][BookingPreDown.Feature][][enableCheckCondition = true
[I][2025-04-02 +80 23:42:46.180][11932, 84][timer][market][BookingPreDown.Feature][][enableBattery = true
[I][2025-04-02 +80 23:42:46.182][11932, 52][kcsdk_temporary-5][market][KingCardPluginManager][][大王卡状态变更：4
[I][2025-04-02 +80 23:42:46.190][11932, 84][timer][market][BookingPreDown.Feature][][enableStorage = true
[I][2025-04-02 +80 23:42:46.191][11932, 84][timer][market][BookingPreDown.Feature][][enableAppBackground = true
[I][2025-04-02 +80 23:42:46.197][11932, 84][timer][market][BookingPreDown.Feature][][enableScreenOff = true
[I][2025-04-02 +80 23:42:46.197][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[I][2025-04-02 +80 23:42:46.197][11932, 84][timer][market][BookingPreDown.Feature][][enableWifi = true
[I][2025-04-02 +80 23:42:46.198][11932, 84][timer][market][BookingPreDown.Feature][][enableRunningTask = true
[I][2025-04-02 +80 23:42:46.198][11932, 84][timer][market][BookingPreDown.Feature][][reqConfigInstallTimoutTime = 5000
[I][2025-04-02 +80 23:42:46.198][11932, 84][timer][market][BookingPreDown.Feature][][minClientPullDataIntevalTime = 300
[I][2025-04-02 +80 23:42:46.199][11932, 84][timer][market][BookingPreDown.Feature][][defaultClientPullDataIntevalTime = 3600
[I][2025-04-02 +80 23:42:46.199][11932, 84][timer][market][BookingPreDown.Feature][][bookingPreSvrFlowStateUpdateIntervalSecs = 30
[I][2025-04-02 +80 23:42:46.260][11932, 37][temporary-1][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 23:42:46.261][11932, 37][temporary-1][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 23:42:46.263][11932, 84][timer][market][BookingPreDown.Feature][][lastPullDataTime = 1743595764008
[I][2025-04-02 +80 23:42:46.263][11932, 84][timer][market][BookingPreDown.Feature][][clientPullDataIntevalTime = 3600
[I][2025-04-02 +80 23:42:46.263][11932, 84][timer][market][BookingPreDown.Feature][][enableForceDualPackageList = 
[I][2025-04-02 +80 23:42:46.263][11932, 1*][main][market][DarkModeObserver][][DarkModeObserver =false
[I][2025-04-02 +80 23:42:46.264][11932, 84][timer][market][BookingPreDown.Feature][][enableForceDualTaskAutoDownload = true
[I][2025-04-02 +80 23:42:46.264][11932, 84][timer][market][CommonTracerFactory][][genTracer:bpd_start_pull
[I][2025-04-02 +80 23:42:46.264][11932, 84][timer][market][TracerImpl][][[bpd_start_pull] onStart, expect events:[], timeout:-1
[I][2025-04-02 +80 23:42:46.266][11932, 42][temporary-3][market][KuiklyPageInfoManager][][initCache cache pageNameList.size() = 191
[I][2025-04-02 +80 23:42:46.267][11932, 84][timer][market][TracerImpl][][[bpd_start_pull] onExpectEventArrived:pull
[I][2025-04-02 +80 23:42:46.272][11932, 1*][main][market][redDot][][MgrTabRedDotManager notifyChange type 个人中心新增监听事件 msg.what = 1388
[I][2025-04-02 +80 23:42:46.272][11932, 1*][main][market][redDot][][notifyChange 个人中心新增监听事件
[I][2025-04-02 +80 23:42:46.280][11932, 39][temporary-2][market][KuiklyPageResourcePreloadTimeJob][][loadLatestKuiklyScenePageResource()
[I][2025-04-02 +80 23:42:46.301][11932, 51][temporary-4][market][KuiklyDynamicResManager][][no need copyNativeKuiklyFilesToStorage, has copied!
[I][2025-04-02 +80 23:42:46.304][11932, 39][temporary-2][market][Kuikly-PageResourceLoader][][loadLatestKuiklyScenePageResource
[I][2025-04-02 +80 23:42:46.304][11932, 39][temporary-2][market][Kuikly-PageResourceLoader][][loadPageResourceByScene sceneId: 100877
[I][2025-04-02 +80 23:42:46.305][11932, 51][temporary-4][market][KuiklyDynamicResManager][][no need copyNativeKuiklyFilesToStorage, has copied!
[I][2025-04-02 +80 23:42:46.306][11932, 51][temporary-4][market][KuiklyDynamicResManager][][no need copyNativeKuiklyFilesToStorage, has copied!
[E][2025-04-02 +80 23:42:46.308][11932, 186][TVK_NetworkDispatcher][market][assistant][][findClass exception err: java.io.IOException
[E][2025-04-02 +80 23:42:46.308][11932, 186][TVK_NetworkDispatcher][market][[TVKHttpDataSource_1012]][][Unable to resolve host "vv.video.qq.com": No address associated with hostname
【plugin_version：111 】
[I][2025-04-02 +80 23:42:46.314][11932, 39][temporary-2][market][Kuikly-PageResourceLoader][][batchFetchResConfigBySceneWithCache sceneId: 100877, forceRefresh: true
[I][2025-04-02 +80 23:42:46.335][11932, 53][temporary-6][market][QDPatchShiplyServiceImpl][][send request versionCode:8924130, isManual:0, flag:0, isForeground:0, buildNo:2819, type:1, activeTime:1743581585303, localCutEocdMd5:f7c9389f7a81de86de6dbbed9e381739, localBaseMd5:, apkMd5:
[I][2025-04-02 +80 23:42:46.339][11932, 37][temporary-1][market][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 23:42:46.340][11932, 37][temporary-1][market][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 23:42:46.345][11932, 84][timer][market][BookingPreDown.timer][][period:3600
[I][2025-04-02 +80 23:42:46.346][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 23:42:46.347][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 23:42:46.347][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 23:42:46.348][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 23:42:46.348][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[E][2025-04-02 +80 23:42:46.349][11932, 185][TVK-VIP-1][market][assistant][][findClass exception err: java.net.UnknownHostException
[E][2025-04-02 +80 23:42:46.349][11932, 185][TVK-VIP-1][market][[TVKPlayer]TVKServerTimeGetter][][[ServerTime] errorcom.tencent.qqlive.tvkplayer.thirdparties.httpclient.HttpDataSource$HttpDataSourceException: Unable to connect to https://vv.video.qq.com/checktime?randnum=0.8564680764163869&guid=wtfguidisemptyhehehe&otype=json【plugin_version：111 】
[E][2025-04-02 +80 23:42:46.349][11932, 185][TVK-VIP-1][market][[TVKPlayer]TVKServerTimeGetter][][[ServerTime] change host, retry【plugin_version：111 】
[I][2025-04-02 +80 23:42:46.354][11932, 54][temporary-7][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.354][11932, 54][temporary-7][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:46.363][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 23:42:46.366][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 23:42:46.366][11932, 54][temporary-7][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.367][11932, 54][temporary-7][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[E][2025-04-02 +80 23:42:46.372][11932, 187][TVK_NetworkDispatcher][market][[TVKHttpDataSource_1013]][][Unable to resolve host "bkvv.video.qq.com": No address associated with hostname
【plugin_version：111 】
[E][2025-04-02 +80 23:42:46.373][11932, 236][TVK-VIP-2][market][[TVKPlayer]TVKServerTimeGetter][][[ServerTime] errorcom.tencent.qqlive.tvkplayer.thirdparties.httpclient.HttpDataSource$HttpDataSourceException: Unable to connect to https://bkvv.video.qq.com/checktime?randnum=0.2624157261611795&guid=wtfguidisemptyhehehe&otype=json【plugin_version：111 】
[E][2025-04-02 +80 23:42:46.373][11932, 236][TVK-VIP-2][market][[TVKPlayer]TVKServerTimeGetter][][[ServerTime] failure, pass wrong time to getvinfo【plugin_version：111 】
[I][2025-04-02 +80 23:42:46.377][11932, 54][temporary-7][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 23:42:46.388][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200387 endTime:1743609600387
[I][2025-04-02 +80 23:42:46.393][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 23:42:46.394][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[E][2025-04-02 +80 23:42:46.397][11932, 627][OkHttp https://rdelivery.qq.com/...][market][RDeliveryProvider_TAG][][RDelivery开关配置拉取失败: java.net.UnknownHostException: Unable to resolve host "rdelivery.qq.com": No address associated with hostname
[I][2025-04-02 +80 23:42:46.398][11932, 627][OkHttp https://rdelivery.qq.com/...][market][RdeliveryConfigFetcher][][Rdelivery refresh configure fail!
[I][2025-04-02 +80 23:42:46.397][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 23:42:46.398][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 23:42:46.418][11932, 54][temporary-7][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 23:42:46.418][11932, 54][temporary-7][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 23:42:46.418][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 23:42:46.455][11932, 39][temporary-2][market][Kuikly-PageResourceLoader][][batchFetchResConfigBySceneWithCache start batch
[I][2025-04-02 +80 23:42:46.623][11932, 39][temporary-2][market][Kuikly-PageResourceLoader][][getResHub: 
[I][2025-04-02 +80 23:42:46.637][11932, 39][temporary-2][market][Kuikly-PageResourceLoader][][batchFetchResConfigBySceneWithCache onResHubReady
[I][2025-04-02 +80 23:42:46.980][11932, 1*][main][market][KingCardUsersUndertakeManager][][showKingCardHintIfNeed return: isKingCard
[E][2025-04-02 +80 23:42:47.040][11932, 627][OkHttp https://rdelivery.qq.com/...][market][ResHub.SceneResLoader][][fetchConfig onFail, java.net.UnknownHostException: Unable to resolve host "rdelivery.qq.com": No address associated with hostname
[I][2025-04-02 +80 23:42:47.042][11932, 627][OkHttp https://rdelivery.qq.com/...][market][Kuikly-PageResourceLoader][][batchFetchResConfigBySceneWithCache refresh complete, isAllSuccess: false
[I][2025-04-02 +80 23:42:47.042][11932, 627][OkHttp https://rdelivery.qq.com/...][market][Kuikly-PageResourceLoader][][loadPageResourceByScene complete
[I][2025-04-02 +80 23:42:47.050][11932, 627][OkHttp https://rdelivery.qq.com/...][market][Kuikly-PageResourceLoader][][batchFetchResConfigByScene needDownLoadRes : []
[I][2025-04-02 +80 23:42:47.051][11932, 627][OkHttp https://rdelivery.qq.com/...][market][ResHub.ResHubBatchLoader][][Start Batch Preload Latest([])...
[I][2025-04-02 +80 23:42:47.058][11932, 627][OkHttp https://rdelivery.qq.com/...][market][Kuikly-PageResourceLoader][][cacheKuiklyResourceId
[I][2025-04-02 +80 23:42:47.060][11932, 51][temporary-4][market][QDFileUtils][][#deleteFile path:/data/user/0/com.tencent.android.qqdownloader/cache/kuikly_resource_id
[I][2025-04-02 +80 23:42:47.175][11932, 51][temporary-4][market][QDFileUtils][][#deleteFile: end, fileExists=false
[I][2025-04-02 +80 23:42:47.176][11932, 51][temporary-4][market][QDFileUtils][][#deleteFile: /data/user/0/com.tencent.android.qqdownloader/cache/kuikly_resource_id
[I][2025-04-02 +80 23:42:47.178][11932, 51][temporary-4][market][FileUtil][][#deleteFile: path=/data/user/0/com.tencent.android.qqdownloader/cache/kuikly_resource_id, deleteResult=true
[I][2025-04-02 +80 23:42:47.197][11932, 51][temporary-4][market][Kuikly-PageResourceLoader][][cacheKuiklyResourceId timeCost: 138
[I][2025-04-02 +80 23:42:47.206][11932, 37][temporary-1][market][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 23:42:47.458][11932, 37][temporary-1][market][AIGCRedDot][][getRedPotType isHasNewTask = false
[I][2025-04-02 +80 23:42:47.479][11932, 37][temporary-1][market][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 23:42:47.519][11932, 37][temporary-1][market][AIGCRedDot][][getRedPotType isHasNewTask = false
[I][2025-04-02 +80 23:42:47.533][11932, 37][temporary-1][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:47.537][11932, 37][temporary-1][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:47.581][11932, 54][temporary-7][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:47.581][11932, 54][temporary-7][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:47.584][11932, 37][temporary-1][market][redDot][][showRedDotWithTabType --- bundle = Bundle[{bubbleNumber=0, tabType=3, showRedDot=false, currentTabType=3}]
[I][2025-04-02 +80 23:42:49.601][11932, 1*][main][market][RedDot][][updateRedDotRunnable
[I][2025-04-02 +80 23:42:49.602][11932, 1*][main][market][redDot][][setRedDotWithType: tabType = 3 bundle = Bundle[{bubbleNumber=0, tabType=3, showRedDot=false, currentTabType=3}]
[I][2025-04-03 +80 00:32:41.123][11932, 51][temporary-4][market][TracerImpl][][[bpd_start_pull] traceOver, type:TIMEOUT
[I][2025-04-03 +80 00:32:41.128][11932, 1*][main][market][WidgetReceiver][][ACTION_DATE_CHANGED
[I][2025-04-03 +80 00:32:41.131][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[I][2025-04-03 +80 00:32:41.132][11932, 51][temporary-4][market][TracerImpl][][[bpd_start_pull] gameOver process suspend, realCost:2994867, traceTimeoutMs:20000
[I][2025-04-03 +80 00:32:41.133][11932, 51][temporary-4][market][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-03 +80 00:32:41.134][11932, 51][temporary-4][market][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-03 +80 00:32:41.545][11932, 629][net_dedicated-629][market][BookingPreDown_PullEngine][][onRequestFailed errorCode:-828
~~~~~ end of mmap ~~~~~[32058,32164][2025-04-03 +0800 00:54:09]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[32058,32164][2025-04-03 +0800 00:54:09]
get mmap time: 1
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77349773312 available:77215555584
log dir space info, capacity:117409054720 free:77194584064 available:77194584064
