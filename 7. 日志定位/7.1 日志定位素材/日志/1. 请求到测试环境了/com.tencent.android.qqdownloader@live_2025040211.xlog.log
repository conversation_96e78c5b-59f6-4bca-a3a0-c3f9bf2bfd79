~~~~~ begin of mmap ~~~~~
[I][2025-04-01 +80 23:22:47.226][29839, 39][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-01 +80 23:22:47.306][29839, 41][temporary-4][live][BinderManager][][connectToServiceOptimize
[I][2025-04-01 +80 23:22:47.385][29839, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-01 +80 23:22:47.386][29839, 1*][main][live][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-01 +80 23:22:47.388][29839, 44][temporary-7][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-01 +80 23:22:47.392][29839, 45][SendEventDispatcher][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-01 +80 23:22:47.392][29839, 36][temporary-1][live][BinderManager][][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-01 +80 23:22:47.393][29839, 45][SendEventDispatcher][live][BinderManager][][addService, service : true
[I][2025-04-01 +80 23:22:47.399][29839, 39][temporary-2][live][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-01 +80 23:22:47.402][29839, 39][temporary-2][live][BinderManager][][addService, service : true
[I][2025-04-01 +80 23:22:47.405][29839, 44][temporary-7][live][BinderManager][][addService, service : true
[I][2025-04-01 +80 23:22:47.405][29839, 36][temporary-1][live][BinderManager][][addService, service : true
[I][2025-04-01 +80 23:22:47.409][29839, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-01 +80 23:22:47.410][29839, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-01 +80 23:22:47.412][29839, 42][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-01 +80 23:22:47.412][29839, 42][temporary-5][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-01 +80 23:22:47.420][29839, 40][temporary-3][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-01 +80 23:22:47.428][29839, 41][temporary-4][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-01 +80 23:22:47.434][29839, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-01 +80 23:22:47.435][29839, 41][temporary-4][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-01 +80 23:22:47.438][29839, 41][temporary-4][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-01 +80 23:22:47.472][29839, 49][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-01 +80 23:22:47.474][29839, 49][temporary-8][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-01 +80 23:22:47.482][29839, 49][temporary-8][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-01 +80 23:22:47.484][29839, 42][temporary-5][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-01 +80 23:22:47.571][29839, 40][temporary-3][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-01 +80 23:22:47.571][29839, 40][temporary-3][live][genQUA][][mQUA: TMAF_892_P_2671/082671&NA/082671/8924130_2671&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2671&V3
[I][2025-04-01 +80 23:22:51.114][29839, 36][temporary-1][live][RdefenseIdleTask][][init
[I][2025-04-01 +80 23:52:42.496][29839, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-01 +80 23:52:42.497][29839, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onDestroy
[I][2025-04-01 +80 23:52:42.519][29839, 44][temporary-7][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
~~~~~ end of mmap ~~~~~[30656,30731][2025-04-02 +0800 11:31:24]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[30656,30731][2025-04-02 +0800 11:31:24]
get mmap time: 33
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77030780928 available:76896563200
log dir space info, capacity:117409054720 free:76875591680 available:76875591680
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][Settings][NameValueCache: false
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922671.cd.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2671', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][RightlySDKManager][initSDK time =264
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][ChannelInfoManager][initConfig: config = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk, false
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][ChannelIdManager][return NA
[I][2025-04-02 +80 11:31:24.690][30656, 39][temporary-2][cache][live][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = live
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = live
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][XLog][init, proccess:live
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][RdefenseInitTask][init
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][KeepAliveMainSwitchManager][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][IoniaStartDaemonProxy][IoniaStartDaemonProxy prepareConnection
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 67, dataMap.size = 321, memSize = 325.408203125
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][RDelivery_DataManager_aa66b6582e_10001_][loadDataFromDisk loadResult = true, cost = 84, dataMap.size = 321, memSize = 325.408203125
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 124, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][YYBLiveAuthService][YYBLiveAuthService onStartCommand
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][addService, service : true
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][queryBinder, binderCode : 124, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][BinderManager][addService, service : true
[E][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][cache][live][ActivityThreadHacker][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 11:31:24.691][30656, 39][temporary-2][live][AstApp][][xlog init finished:live
[I][2025-04-02 +80 11:31:24.711][30656, 36][temporary-1][live][ActivityThreadHacker][][start Hook successful, dur = 73
[I][2025-04-02 +80 11:31:25.883][30656, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onBind
[I][2025-04-02 +80 11:31:25.885][30656, 1*][main][live][IoniaStartDaemonProxy][][createAccountBinder, binderCode: 0, binderName: 
[I][2025-04-02 +80 11:31:25.885][30656, 1*][main][live][IoniaStartDaemonProxy][][onAccountServiceBind by ionia service
[I][2025-04-02 +80 11:31:25.917][30656, 1*][main][live][MainBinderManager][][tryToConnect process:live
[I][2025-04-02 +80 11:31:25.923][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[I][2025-04-02 +80 11:31:25.924][30656, 42][temporary-5][live][MainBinderManager][][tryToConnect process:live
[E][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.924][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.925][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.925][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.925][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.925][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.925][30656, 1*][main][live][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-02 +80 11:31:25.925][30656, 1*][main][live][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-02 +80 11:31:25.926][30656, 1*][main][live][report_tag][][hookAms = false
[I][2025-04-02 +80 11:31:25.962][30656, 1*][main][live][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:live
server:android.os.BinderProxy@fc5e462
[I][2025-04-02 +80 11:31:25.967][30656, 72][Binder:30656_7][live][Phantom][][Phantom.getBundle from live process
[I][2025-04-02 +80 11:31:25.969][30656, 72][Binder:30656_7][live][MainBinderManager][][queryBinder, binderCode : 1033 binderManager:true
[I][2025-04-02 +80 11:31:25.970][30656, 72][Binder:30656_7][live][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 11:31:29.484][30656, 49][temporary-7][live][RdefenseIdleTask][][init
[I][2025-04-02 +80 11:31:29.500][30656, 49][temporary-7][live][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 11:31:29.546][30656, 41][temporary-4][live][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-02 +80 11:31:31.473][30656, 48][BeaconReportHandler][live][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:31:31.474][30656, 48][BeaconReportHandler][live][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:31:31.483][30656, 48][BeaconReportHandler][live][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 11:31:31.492][30656, 48][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:31:31.494][30656, 48][BeaconReportHandler][live][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:31:31.496][30656, 48][BeaconReportHandler][live][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 11:31:36.550][30656, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-02 +80 11:31:53.072][30656, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
~~~~~ end of mmap ~~~~~[32170,32254][2025-04-02 +0800 11:32:06]
