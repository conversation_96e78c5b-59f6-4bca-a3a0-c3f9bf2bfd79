[I][2025-04-02 +80 14:36:23.935][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 450206
[I][2025-04-02 +80 14:36:23.935][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:36:23.938][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11066秒]
[I][2025-04-02 +80 14:36:23.942][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:36:23.942][31209, 40][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:36:24.092][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427413136 , reportEventTime = 427423137
[W][2025-04-02 +80 14:36:24.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:24.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:26.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:27.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:29.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:30.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:33.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:36:34.103][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 427423138 , reportEventTime = 427433148
[W][2025-04-02 +80 14:36:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:35.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:38.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:38.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:39.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:39.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:41.439][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:41.440][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:42.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:42.941][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:36:44.113][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 427433148 , reportEventTime = 427443158
[W][2025-04-02 +80 14:36:44.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:44.441][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:45.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:36:47.490][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:36:47.491][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:36:47.492][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:36:47.492][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:36:47.493][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:36:47.494][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:36:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:48.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:50.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:53.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:36:53.948][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 480219
[I][2025-04-02 +80 14:36:53.949][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:36:53.952][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:36:53.955][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11096秒]
[I][2025-04-02 +80 14:36:53.957][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:36:54.114][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 427443159 , reportEventTime = 427453159
[W][2025-04-02 +80 14:36:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:54.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:36:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:36:59.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:37:00.013][31209, 46][temporary-7][daemon][FLog_login_log][][[temporary-7]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:37:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:02.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:03.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:37:04.122][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 6 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10006 , taskStartElapsedRealtimeMs = 427453160 , reportEventTime = 427463166
[W][2025-04-02 +80 14:37:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:06.946][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:08.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:09.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:11.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:12.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:37:14.130][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 7 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10007 , taskStartElapsedRealtimeMs = 427463167 , reportEventTime = 427473174
[W][2025-04-02 +80 14:37:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:14.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:18.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:18.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:20.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:21.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:37:23.965][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 510237
[I][2025-04-02 +80 14:37:23.965][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:37:23.973][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:37:23.974][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11127秒]
[I][2025-04-02 +80 14:37:23.975][31209, 47][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:37:24.131][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427473175 , reportEventTime = 427483176
[W][2025-04-02 +80 14:37:24.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:24.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:26.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:29.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:30.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:37:33.531][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=82
[I][2025-04-02 +80 14:37:33.531][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=82
[I][2025-04-02 +80 14:37:33.532][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=82
[W][2025-04-02 +80 14:37:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:37:34.142][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 427483176 , reportEventTime = 427493187
[W][2025-04-02 +80 14:37:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:35.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:36.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:38.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:39.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:39.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:37:44.150][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 427493187 , reportEventTime = 427503195
[W][2025-04-02 +80 14:37:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:44.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:47.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:37:48.950][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:37:48.952][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:37:48.953][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:37:48.954][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:37:48.955][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:37:48.956][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:37:50.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:50.446][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:53.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:37:53.979][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 540251
[I][2025-04-02 +80 14:37:53.979][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:37:53.982][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11157秒]
[I][2025-04-02 +80 14:37:53.983][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:37:53.983][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:37:54.152][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 2 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10002 , taskStartElapsedRealtimeMs = 427503195 , reportEventTime = 427513197
[W][2025-04-02 +80 14:37:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:54.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:37:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:37:59.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:38:00.010][31209, 44][temporary-5][daemon][FLog_login_log][][[temporary-5]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:38:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:02.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:03.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:38:04.157][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 5 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10005 , taskStartElapsedRealtimeMs = 427513197 , reportEventTime = 427523202
[W][2025-04-02 +80 14:38:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:08.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:09.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:11.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:12.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:38:14.166][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 427523202 , reportEventTime = 427533211
[W][2025-04-02 +80 14:38:14.444][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:14.449][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:18.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:21.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:38:23.993][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 570264
[I][2025-04-02 +80 14:38:23.994][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:38:23.996][31209, 40][temporary-3][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11187秒]
[I][2025-04-02 +80 14:38:23.997][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:38:23.999][31209, 40][temporary-3][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:38:24.167][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427533211 , reportEventTime = 427543212
[W][2025-04-02 +80 14:38:24.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:24.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:26.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:29.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:30.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:38:34.174][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 6 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10006 , taskStartElapsedRealtimeMs = 427543213 , reportEventTime = 427553219
[W][2025-04-02 +80 14:38:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:35.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:36.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:38.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:39.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:39.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:38:44.182][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 427553219 , reportEventTime = 427563227
[W][2025-04-02 +80 14:38:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:44.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:47.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:48.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:38:50.450][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:38:50.451][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:38:50.452][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:38:50.453][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:38:50.453][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:38:50.454][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:38:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:53.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:38:54.009][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250402 , nowCount = 0 , lastExecuteTime = 1743575333728
[I][2025-04-02 +80 14:38:54.009][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-02 +80 14:38:54.012][31209, 38][temporary-2][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[I][2025-04-02 +80 14:38:54.017][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:38:54.017][31209, 43][temporary-4][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11217秒]
[I][2025-04-02 +80 14:38:54.019][31209, 43][temporary-4][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:38:54.019][31209, 38][temporary-2][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[I][2025-04-02 +80 14:38:54.183][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427563227 , reportEventTime = 427573228
[W][2025-04-02 +80 14:38:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:54.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:56.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:38:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:38:59.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:39:00.012][31209, 36][temporary-1][daemon][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:39:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:02.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:03.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:39:04.194][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 427573229 , reportEventTime = 427583239
[W][2025-04-02 +80 14:39:05.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:08.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:09.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:11.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:12.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:12.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:39:14.202][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 7 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10007 , taskStartElapsedRealtimeMs = 427583240 , reportEventTime = 427593247
[W][2025-04-02 +80 14:39:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:14.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:15.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:18.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:20.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:20.446][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:21.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:39:24.024][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 30015
[I][2025-04-02 +80 14:39:24.024][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:39:24.027][31209, 45][temporary-6][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:39:24.028][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11247秒]
[I][2025-04-02 +80 14:39:24.030][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:39:24.203][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427593247 , reportEventTime = 427603248
[W][2025-04-02 +80 14:39:24.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:24.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:39:26.146][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=83
[I][2025-04-02 +80 14:39:26.147][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=83
[I][2025-04-02 +80 14:39:26.150][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=83
[W][2025-04-02 +80 14:39:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:26.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:29.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:30.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:39:34.208][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 4 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10004 , taskStartElapsedRealtimeMs = 427603249 , reportEventTime = 427613253
[W][2025-04-02 +80 14:39:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:35.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:36.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:38.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:38.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:39.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:39.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:39:44.218][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 427613254 , reportEventTime = 427623263
[W][2025-04-02 +80 14:39:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:44.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:47.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:48.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:50.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:39:51.988][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:39:51.989][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:39:51.991][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:39:51.992][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:39:51.993][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:39:51.994][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:39:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:53.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:39:54.040][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 60031
[I][2025-04-02 +80 14:39:54.040][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:39:54.041][31209, 43][temporary-4][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11277秒]
[I][2025-04-02 +80 14:39:54.043][31209, 43][temporary-4][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:39:54.044][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:39:54.219][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427623263 , reportEventTime = 427633264
[W][2025-04-02 +80 14:39:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:54.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:56.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:39:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:39:59.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:40:00.018][31209, 43][temporary-4][daemon][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:40:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:02.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:03.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:40:04.230][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 427633264 , reportEventTime = 427643275
[W][2025-04-02 +80 14:40:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:08.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:09.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:11.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:12.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:40:14.236][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 6 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10006 , taskStartElapsedRealtimeMs = 427643275 , reportEventTime = 427653281
[W][2025-04-02 +80 14:40:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:14.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:15.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:17.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:18.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:21.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:40:24.059][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 90050
[I][2025-04-02 +80 14:40:24.059][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:40:24.062][31209, 36][temporary-1][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11307秒]
[I][2025-04-02 +80 14:40:24.064][31209, 45][temporary-6][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:40:24.065][31209, 36][temporary-1][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:40:24.237][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427653281 , reportEventTime = 427663282
[W][2025-04-02 +80 14:40:24.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:24.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:26.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:29.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:30.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:32.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:33.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:40:34.246][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 427663283 , reportEventTime = 427673291
[W][2025-04-02 +80 14:40:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:35.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:36.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:38.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:39.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:39.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:42.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:42.942][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:40:44.254][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 7 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10007 , taskStartElapsedRealtimeMs = 427673292 , reportEventTime = 427683299
[W][2025-04-02 +80 14:40:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:44.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:47.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:48.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:50.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:40:53.450][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:40:53.451][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:40:53.452][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:40:53.453][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:40:53.454][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:40:53.455][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 14:40:54.072][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 120063
[I][2025-04-02 +80 14:40:54.072][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:40:54.075][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11337秒]
[I][2025-04-02 +80 14:40:54.076][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:40:54.078][31209, 40][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:40:54.256][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427683299 , reportEventTime = 427693300
[W][2025-04-02 +80 14:40:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:54.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:40:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:40:59.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:41:00.015][31209, 36][temporary-1][daemon][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:41:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:02.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:03.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:41:04.267][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 427693301 , reportEventTime = 427703312
[W][2025-04-02 +80 14:41:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:08.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:09.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:11.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:12.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:41:14.278][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 427703312 , reportEventTime = 427713323
[W][2025-04-02 +80 14:41:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:14.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:18.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:21.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:41:24.088][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 150079
[I][2025-04-02 +80 14:41:24.088][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:41:24.093][31209, 38][temporary-2][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11367秒]
[I][2025-04-02 +80 14:41:24.093][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:41:24.094][31209, 38][temporary-2][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:41:24.279][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 427713324 , reportEventTime = 427723324
[W][2025-04-02 +80 14:41:24.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:24.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:26.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:41:29.049][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=84
[I][2025-04-02 +80 14:41:29.051][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=84
[I][2025-04-02 +80 14:41:29.053][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=84
[W][2025-04-02 +80 14:41:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:29.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:30.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:33.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:41:34.290][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 427723325 , reportEventTime = 427733335
[W][2025-04-02 +80 14:41:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:35.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:38.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:39.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:39.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:41:44.301][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 427733336 , reportEventTime = 427743346
[W][2025-04-02 +80 14:41:44.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:44.441][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:47.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:48.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:50.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:51.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:53.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:41:54.103][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 180093
[I][2025-04-02 +80 14:41:54.103][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:41:54.106][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11397秒]
[I][2025-04-02 +80 14:41:54.106][31209, 44][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:41:54.108][31209, 45][temporary-6][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:41:54.302][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427743346 , reportEventTime = 427753347
[W][2025-04-02 +80 14:41:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:41:54.951][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:41:54.952][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:41:54.953][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:41:54.954][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:41:54.955][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:41:54.956][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:41:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:41:59.443][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:41:59.446][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:42:00.013][31209, 45][temporary-6][daemon][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:42:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:02.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:03.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:03.946][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:42:04.310][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 427753347 , reportEventTime = 427763355
[W][2025-04-02 +80 14:42:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:08.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:09.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:09.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:11.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:12.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:42:14.316][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 6 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10006 , taskStartElapsedRealtimeMs = 427763355 , reportEventTime = 427773361
[W][2025-04-02 +80 14:42:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:14.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:18.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:21.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:42:24.116][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 210107
[I][2025-04-02 +80 14:42:24.118][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:42:24.122][31209, 38][temporary-2][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:42:24.122][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11427秒]
[I][2025-04-02 +80 14:42:24.124][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:42:24.318][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427773362 , reportEventTime = 427783363
[W][2025-04-02 +80 14:42:24.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:24.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:26.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:29.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:30.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:42:32.260][31209, 1*][main][daemon][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[W][2025-04-02 +80 14:42:32.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:33.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:42:34.320][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 2 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10002 , taskStartElapsedRealtimeMs = 427783363 , reportEventTime = 427793365
[W][2025-04-02 +80 14:42:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:35.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:38.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:39.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:39.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:41.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:42.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:42:44.331][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 427793365 , reportEventTime = 427803375
[W][2025-04-02 +80 14:42:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:44.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:45.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:47.443][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:48.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:50.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:53.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:42:54.130][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 240121
[I][2025-04-02 +80 14:42:54.130][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:42:54.134][31209, 44][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11457秒]
[I][2025-04-02 +80 14:42:54.135][31209, 46][temporary-7][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:42:54.136][31209, 44][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:42:54.332][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 427803376 , reportEventTime = 427813376
[W][2025-04-02 +80 14:42:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:54.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:42:56.486][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:42:56.487][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:42:56.487][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:42:56.488][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:42:56.489][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:42:56.489][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:42:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:42:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:42:59.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:43:00.016][31209, 43][temporary-4][daemon][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:43:00.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:02.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:02.442][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:03.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:43:04.342][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 427813377 , reportEventTime = 427823387
[W][2025-04-02 +80 14:43:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:08.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:09.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:11.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:12.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:43:14.351][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 427823388 , reportEventTime = 427833396
[W][2025-04-02 +80 14:43:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:14.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:18.943][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:18.946][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:43:19.085][31209, 58][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[W][2025-04-02 +80 14:43:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:20.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:21.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:43:24.147][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 270137
[I][2025-04-02 +80 14:43:24.147][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:43:24.150][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11487秒]
[I][2025-04-02 +80 14:43:24.151][31209, 36][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:43:24.152][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:43:24.352][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 427833397 , reportEventTime = 427843397
[W][2025-04-02 +80 14:43:24.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:24.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:26.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:26.442][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:29.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:30.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:43:31.909][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=85
[I][2025-04-02 +80 14:43:31.909][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=85
[I][2025-04-02 +80 14:43:31.909][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=85
[W][2025-04-02 +80 14:43:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:43:34.363][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 427843398 , reportEventTime = 427853408
[W][2025-04-02 +80 14:43:35.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:35.448][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:38.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:39.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:39.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:41.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:43:44.367][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 4 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10004 , taskStartElapsedRealtimeMs = 427853408 , reportEventTime = 427863412
[W][2025-04-02 +80 14:43:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:44.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:47.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:48.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:50.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:53.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:43:54.164][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 300154
[I][2025-04-02 +80 14:43:54.164][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:43:54.166][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11517秒]
[I][2025-04-02 +80 14:43:54.167][31209, 45][temporary-6][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:43:54.168][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:43:54.368][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 427863413 , reportEventTime = 427873413
[W][2025-04-02 +80 14:43:54.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:54.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:43:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:43:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:43:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:43:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:43:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:43:57.946][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:43:57.946][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:43:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:43:59.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:44:00.018][31209, 36][temporary-1][daemon][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:44:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:02.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:03.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:44:04.376][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 427873413 , reportEventTime = 427883421
[W][2025-04-02 +80 14:44:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:06.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:08.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:09.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:11.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:12.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:44:14.387][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 427883422 , reportEventTime = 427893432
[W][2025-04-02 +80 14:44:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:14.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:15.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:18.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:21.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:44:24.173][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 330163
[I][2025-04-02 +80 14:44:24.173][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:44:24.176][31209, 38][temporary-2][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11547秒]
[I][2025-04-02 +80 14:44:24.176][31209, 40][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:44:24.178][31209, 38][temporary-2][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:44:24.389][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 2 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10002 , taskStartElapsedRealtimeMs = 427893432 , reportEventTime = 427903434
[W][2025-04-02 +80 14:44:24.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:24.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:26.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:27.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:29.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:30.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:32.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:44:34.398][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 427903434 , reportEventTime = 427913443
[W][2025-04-02 +80 14:44:35.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:35.443][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:36.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:38.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:39.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:39.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:44:44.406][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 427913443 , reportEventTime = 427923451
[W][2025-04-02 +80 14:44:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:44.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:47.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:48.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:50.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:51.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:53.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:44:54.191][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 360181
[I][2025-04-02 +80 14:44:54.191][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:44:54.194][31209, 38][temporary-2][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11577秒]
[I][2025-04-02 +80 14:44:54.194][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:44:54.195][31209, 38][temporary-2][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:44:54.407][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 427923451 , reportEventTime = 427933452
[W][2025-04-02 +80 14:44:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:54.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:44:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:44:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:44:59.481][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:44:59.482][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:44:59.483][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:44:59.484][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:44:59.485][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:44:59.485][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 14:45:00.010][31209, 44][temporary-5][daemon][FLog_login_log][][[temporary-5]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:45:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:02.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:03.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:45:04.418][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 427933452 , reportEventTime = 427943463
[W][2025-04-02 +80 14:45:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:05.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:08.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:09.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:09.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:11.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:12.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:45:14.428][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 427943464 , reportEventTime = 427953473
[W][2025-04-02 +80 14:45:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:14.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:18.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:20.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:21.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:45:24.208][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 390199
[I][2025-04-02 +80 14:45:24.208][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:45:24.212][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11607秒]
[I][2025-04-02 +80 14:45:24.213][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:45:24.216][31209, 47][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:45:24.429][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 427953474 , reportEventTime = 427963474
[W][2025-04-02 +80 14:45:24.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:24.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:26.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:26.442][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:45:28.420][31209, 69][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 14:45:28.421][31209, 69][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 14:45:28.444][31209, 38][temporary-2][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 14:45:28.444][31209, 38][temporary-2][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 14:45:28.448][31209, 40][temporary-3][daemon][LogTunnel][][processUAXXX 1
[W][2025-04-02 +80 14:45:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:29.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:30.018][31209, 215][LogProcessService-4][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 14:45:30.021][31209, 215][LogProcessService-4][daemon][HttpNetWorkTaskV2][][[507]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:507,useHttp2:false
[I][2025-04-02 +80 14:45:30.048][31209, 471][protocalManagerStatReport-471][daemon][HttpNetWorkTaskV2][][[507]callStart
[I][2025-04-02 +80 14:45:30.048][31209, 471][protocalManagerStatReport-471][daemon][HttpNetWorkTaskV2][][[507]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-02 +80 14:45:30.081][31209, 471][protocalManagerStatReport-471][daemon][CustomDns-mayybstatnew.3g.qq.com][][lookupImpl ret:[/**************, /*************]
[I][2025-04-02 +80 14:45:30.082][31209, 471][protocalManagerStatReport-471][daemon][HttpNetWorkTaskV2][][[507]connectStart
[I][2025-04-02 +80 14:45:30.095][31209, 471][protocalManagerStatReport-471][daemon][HttpNetWorkTaskV2][][[507]connectEnd protocol:http/1.1
[I][2025-04-02 +80 14:45:30.096][31209, 471][protocalManagerStatReport-471][daemon][HttpNetWorkTaskV2][][[507]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 14:45:30.127][31209, 471][protocalManagerStatReport-471][daemon][HttpNetWorkTaskV2][][[507]callEnd
[I][2025-04-02 +80 14:45:30.138][31209, 471][protocalManagerStatReport-471][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-02 +80 14:45:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:30.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:45:34.434][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 4 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10004 , taskStartElapsedRealtimeMs = 427963475 , reportEventTime = 427973479
[I][2025-04-02 +80 14:45:34.798][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 14:45:34.800][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[I][2025-04-02 +80 14:45:34.800][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=86
[W][2025-04-02 +80 14:45:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:35.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:38.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:39.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:39.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:42.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:45:44.442][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 7 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10007 , taskStartElapsedRealtimeMs = 427973479 , reportEventTime = 427983486
[E][2025-04-02 +80 14:45:44.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:47.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:47.441][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:48.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:50.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:53.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:45:54.222][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 420212
[I][2025-04-02 +80 14:45:54.222][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:45:54.229][31209, 45][temporary-6][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:45:54.230][31209, 40][temporary-3][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11637秒]
[I][2025-04-02 +80 14:45:54.232][31209, 40][temporary-3][daemon][CmdMetrics][][all        1        100.0        105      未探测          {}                  
[I][2025-04-02 +80 14:45:54.233][31209, 40][temporary-3][daemon][CmdMetrics][][stat       1        100.0        105      未探测          {}                  
[I][2025-04-02 +80 14:45:54.442][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 427983487 , reportEventTime = 427993487
[W][2025-04-02 +80 14:45:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:54.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:57.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:45:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:45:59.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:46:00.013][31209, 46][temporary-7][daemon][FLog_login_log][][[temporary-7]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:46:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:46:00.951][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:46:00.952][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:46:00.953][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:46:00.954][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:46:00.955][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:46:00.956][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:46:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:02.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:03.947][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:46:04.447][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 3 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10003 , taskStartElapsedRealtimeMs = 427993488 , reportEventTime = 428003491
[W][2025-04-02 +80 14:46:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:06.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:08.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:09.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:11.442][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:12.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:14.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:46:14.453][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 6 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10006 , taskStartElapsedRealtimeMs = 428003492 , reportEventTime = 428013498
[W][2025-04-02 +80 14:46:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:18.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:21.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:23.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:46:24.236][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 450227
[I][2025-04-02 +80 14:46:24.236][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:46:24.238][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11667秒]
[I][2025-04-02 +80 14:46:24.239][31209, 45][temporary-6][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:46:24.239][31209, 47][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:46:24.454][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 428013499 , reportEventTime = 428023499
[W][2025-04-02 +80 14:46:24.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:24.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:26.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:26.448][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:29.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:30.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:46:34.465][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 428023500 , reportEventTime = 428033510
[W][2025-04-02 +80 14:46:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:35.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:36.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:38.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:39.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:39.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:44.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:46:44.470][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 4 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10004 , taskStartElapsedRealtimeMs = 428033511 , reportEventTime = 428043515
[W][2025-04-02 +80 14:46:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:47.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:48.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:50.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:53.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:46:54.248][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 480238
[I][2025-04-02 +80 14:46:54.248][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:46:54.251][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11697秒]
[I][2025-04-02 +80 14:46:54.252][31209, 44][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:46:54.253][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:46:54.472][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 428043515 , reportEventTime = 428053516
[W][2025-04-02 +80 14:46:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:54.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:46:59.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:46:59.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:47:00.016][31209, 47][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:47:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:00.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:47:02.451][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:47:02.452][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:47:02.453][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:47:02.454][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:47:02.455][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:47:02.456][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:47:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:03.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:47:04.482][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 428053517 , reportEventTime = 428063527
[W][2025-04-02 +80 14:47:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:08.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:09.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:11.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:12.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:14.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:14.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:47:14.486][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 4 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10004 , taskStartElapsedRealtimeMs = 428063527 , reportEventTime = 428073531
[W][2025-04-02 +80 14:47:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:17.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:18.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:21.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:47:24.260][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 510251
[I][2025-04-02 +80 14:47:24.262][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:47:24.264][31209, 40][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:47:24.264][31209, 46][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11727秒]
[I][2025-04-02 +80 14:47:24.265][31209, 46][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:47:24.487][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 428073531 , reportEventTime = 428083532
[W][2025-04-02 +80 14:47:24.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:24.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:26.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:27.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:29.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:30.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:32.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:33.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:47:34.498][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 428083532 , reportEventTime = 428093542
[W][2025-04-02 +80 14:47:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:35.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:47:37.686][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=87
[I][2025-04-02 +80 14:47:37.687][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=87
[I][2025-04-02 +80 14:47:37.687][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=87
[W][2025-04-02 +80 14:47:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:38.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:39.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:39.942][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:41.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:44.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:47:44.505][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 7 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10007 , taskStartElapsedRealtimeMs = 428093543 , reportEventTime = 428103550
[W][2025-04-02 +80 14:47:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:45.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:47.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:48.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:50.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:51.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:53.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:47:54.276][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 540267
[I][2025-04-02 +80 14:47:54.277][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:47:54.281][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11757秒]
[I][2025-04-02 +80 14:47:54.282][31209, 43][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:47:54.283][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:47:54.507][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 428103550 , reportEventTime = 428113551
[W][2025-04-02 +80 14:47:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:54.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:57.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:47:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:47:59.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:48:00.015][31209, 47][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:48:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:00.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:02.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:03.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:48:03.989][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:48:03.990][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:48:03.991][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:48:03.992][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:48:03.993][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:48:03.993][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 14:48:04.516][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 428113552 , reportEventTime = 428123561
[W][2025-04-02 +80 14:48:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:05.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:08.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:09.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:09.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:11.442][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:12.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:14.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:14.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:48:14.528][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 428123562 , reportEventTime = 428133573
[W][2025-04-02 +80 14:48:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:17.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:18.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:21.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:23.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:23.441][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:48:24.294][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 570285
[I][2025-04-02 +80 14:48:24.294][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:48:24.297][31209, 46][temporary-7][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:48:24.297][31209, 36][temporary-1][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11787秒]
[I][2025-04-02 +80 14:48:24.299][31209, 36][temporary-1][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:48:24.530][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 2 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10002 , taskStartElapsedRealtimeMs = 428133573 , reportEventTime = 428143575
[W][2025-04-02 +80 14:48:24.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:24.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:26.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:29.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:30.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:32.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:48:34.538][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 428143575 , reportEventTime = 428153583
[W][2025-04-02 +80 14:48:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:35.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:36.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:36.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:38.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:38.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:39.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:39.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:42.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:44.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:48:44.549][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 428153583 , reportEventTime = 428163594
[W][2025-04-02 +80 14:48:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:45.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:47.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:47.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:48.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:50.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:51.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:53.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:53.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:48:54.306][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250402 , nowCount = 0 , lastExecuteTime = 1743575934009
[I][2025-04-02 +80 14:48:54.306][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-02 +80 14:48:54.310][31209, 36][temporary-1][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[I][2025-04-02 +80 14:48:54.311][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11817秒]
[I][2025-04-02 +80 14:48:54.311][31209, 40][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:48:54.312][31209, 47][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:48:54.317][31209, 36][temporary-1][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[I][2025-04-02 +80 14:48:54.550][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 428163594 , reportEventTime = 428173594
[W][2025-04-02 +80 14:48:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:54.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:56.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:57.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:57.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:48:59.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:48:59.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:48:59.555][31209, 89][common_task_pool-89][daemon][halley-cloud-StateHandler][][onDisconnected
[I][2025-04-02 +80 14:48:59.555][31209, 1*][main][daemon][BookingPreDown.LongConnEngine][][onDisconnected
[I][2025-04-02 +80 14:48:59.555][31209, 1*][main][daemon][UpdateBookingLongConnEngine][][onDisconnected
[I][2025-04-02 +80 14:49:00.013][31209, 38][temporary-2][daemon][FLog_login_log][][[temporary-2]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 14:49:00.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:00.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:02.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:02.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:03.942][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:03.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:04.553][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 3 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10003 , taskStartElapsedRealtimeMs = 428173595 , reportEventTime = 428183598
[W][2025-04-02 +80 14:49:05.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 14:49:05.451][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 14:49:05.452][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 14:49:05.453][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 14:49:05.454][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 14:49:05.455][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 14:49:05.456][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 14:49:06.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:06.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:08.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:08.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:09.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:09.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:11.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:11.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:12.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:12.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:14.442][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:14.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:14.562][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 428183598 , reportEventTime = 428193606
[W][2025-04-02 +80 14:49:15.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:15.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:17.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:17.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:18.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:18.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:20.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:20.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:21.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:21.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:23.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:23.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:24.323][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 30015
[I][2025-04-02 +80 14:49:24.323][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:49:24.327][31209, 47][temporary-8][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:49:24.328][31209, 45][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11847秒]
[I][2025-04-02 +80 14:49:24.330][31209, 45][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:49:24.563][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 428193607 , reportEventTime = 428203607
[W][2025-04-02 +80 14:49:24.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:24.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:26.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:26.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:27.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:27.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:29.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:29.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:30.319][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=88
[I][2025-04-02 +80 14:49:30.319][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=88
[I][2025-04-02 +80 14:49:30.320][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=true;Battery Level=88
[W][2025-04-02 +80 14:49:30.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:30.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:32.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:32.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:33.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:33.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:34.572][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 428203608 , reportEventTime = 428213617
[W][2025-04-02 +80 14:49:35.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:35.443][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:36.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:36.941][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:38.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:38.442][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:39.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:39.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:41.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:41.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:42.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:42.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:44.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:44.444][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:44.582][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 428213617 , reportEventTime = 428223627
[W][2025-04-02 +80 14:49:45.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:45.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:47.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:47.443][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:48.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:48.945][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:50.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:50.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:51.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:51.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:53.143][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=88
[I][2025-04-02 +80 14:49:53.143][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=88
[I][2025-04-02 +80 14:49:53.143][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=88
[W][2025-04-02 +80 14:49:53.440][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:53.443][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:54.336][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 60029
[I][2025-04-02 +80 14:49:54.336][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 14:49:54.339][31209, 46][temporary-7][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 14:49:54.339][31209, 38][temporary-2][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [11877秒]
[I][2025-04-02 +80 14:49:54.340][31209, 38][temporary-2][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 14:49:54.584][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 2 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10002 , taskStartElapsedRealtimeMs = 428223627 , reportEventTime = 428233629
[W][2025-04-02 +80 14:49:54.941][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:54.943][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:49:56.441][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:56.445][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:57.682][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=88
[I][2025-04-02 +80 14:49:57.682][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=88
[I][2025-04-02 +80 14:49:57.682][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=88
[I][2025-04-02 +80 14:49:57.738][31209, 313][Binder:31209_D][daemon][Tinker.ProcessLifecycle][][App是进后台检查: activityCount=1,hasVisibleActivity=true
[I][2025-04-02 +80 14:49:57.758][31209, 313][Binder:31209_D][daemon][Tinker.ProcessLifecycle][][App是进后台检查: activityCount=0,hasVisibleActivity=false
[I][2025-04-02 +80 14:49:57.758][31209, 313][Binder:31209_D][daemon][Tinker.ProcessLifecycle][][【应用宝】进入后台:{"app_time":1582967, "last_scene":2035}
[I][2025-04-02 +80 14:49:57.786][31209, 313][Binder:31209_D][daemon][LogTunnel][][processUAXXX 1
[W][2025-04-02 +80 14:49:57.940][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:57.944][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 14:49:58.211][31209, 1*][main][daemon][UserStateReporter][][onAppStateChangeToMainThread: isFront = false, currentPageId = 2035, isAppFront = true
[I][2025-04-02 +80 14:49:58.211][31209, 1*][main][daemon][UserStateReporter][][onAppStateChange: isFront = false , currentPageId = 2035 , isAppFront = true
[I][2025-04-02 +80 14:49:58.211][31209, 1*][main][daemon][UserStateReporter][][onAppStateChange: app to background>>>> stayDurationMs = 1583407 , currentPageId = 2035
[I][2025-04-02 +80 14:49:58.230][31209, 1*][main][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 14:49:58.231][31209, 1*][main][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 14:49:58.232][31209, 1*][main][daemon][UserStateReporter][][reportUserStayDuration: success, durationMs = 1583407 , scene = 2035
[I][2025-04-02 +80 14:49:58.233][31209, 1*][main][daemon][UserStateReporter][][stopHeartbeatReport: called.
[I][2025-04-02 +80 14:49:58.233][31209, 1*][main][daemon][UserStateReporter][][stopHeartbeatReport: send MSG_ON_APP_BACKGROUND end, calculateTimeMs = 428237278
[I][2025-04-02 +80 14:49:58.233][31209, 1*][main][daemon][UserStateReporter][][onAppStateChange: isAppFront = false , currentPageId = 2035
[I][2025-04-02 +80 14:49:58.234][31209, 1*][main][daemon][GlobalMonitor][][app go background, process:daemon
[I][2025-04-02 +80 14:49:58.236][31209, 1*][main][daemon][download-floating-window][][receive UI_EVENT_APP_GOBACKGROUND
[I][2025-04-02 +80 14:49:58.237][31209, 124][UserStateReporter-thread][daemon][UserStateReporter][][handleMessage: MSG_ON_APP_BACKGROUND , stopHeartbeatTimeMs = 428237278
[I][2025-04-02 +80 14:49:58.237][31209, 124][UserStateReporter-thread][daemon][UserStateReporter][][getHeartbeatReportRecord: inDayReportRecordStr = 1743523200000|21|0
[I][2025-04-02 +80 14:49:58.238][31209, 124][UserStateReporter-thread][daemon][UserStateReporter][][getHeartbeatReportRecord: time is valid 1743523200000|21|0
[I][2025-04-02 +80 14:49:58.238][31209, 124][UserStateReporter-thread][daemon][UserStateReporter][][onAppBackground: heartbeatStartTime = 426681712 , stopHeartbeatTimeMs = 428237278 , reportRecord = HeartbeatReportRecord(times=21, preReportStayDuration=0)
[I][2025-04-02 +80 14:49:58.238][31209, 124][UserStateReporter-thread][daemon][UserStateReporter][][onAppBackground: reset tempDuration
[I][2025-04-02 +80 14:49:58.238][31209, 124][UserStateReporter-thread][daemon][UserStateReporter][][updateTodayHeartbeatReportConfig: reportRecord = 1743523200000|21|0
[I][2025-04-02 +80 14:49:58.272][31209, 46][temporary-7][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 14:49:58.272][31209, 46][temporary-7][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 14:49:58.273][31209, 46][temporary-7][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 14:49:58.303][31209, 257][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_OFF
[W][2025-04-02 +80 14:49:59.478][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:49:59.530][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 14:50:00.119][31209, 216][LogProcessService-5][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 14:50:00.120][31209, 216][LogProcessService-5][daemon][HttpNetWorkTaskV2][][[508]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:508,useHttp2:false
[I][2025-04-02 +80 14:50:00.136][31209, 475][protocalManagerStatReport-475][daemon][HttpNetWorkTaskV2][][[508]callStart
[I][2025-04-02 +80 14:50:00.137][31209, 475][protocalManagerStatReport-475][daemon][HttpNetWorkTaskV2][][[508]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 14:50:00.139][31209, 475][protocalManagerStatReport-475][daemon][HttpNetWorkTaskV2][][[508]connectStart
[I][2025-04-02 +80 14:50:00.169][31209, 475][protocalManagerStatReport-475][daemon][HttpNetWorkTaskV2][][[508]connectEnd protocol:http/1.1
[I][2025-04-02 +80 14:50:00.169][31209, 475][protocalManagerStatReport-475][daemon][HttpNetWorkTaskV2][][[508]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 14:50:00.216][31209, 475][protocalManagerStatReport-475][daemon][HttpNetWorkTaskV2][][[508]callEnd
[I][2025-04-02 +80 14:50:00.224][31209, 475][protocalManagerStatReport-475][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-02 +80 14:50:00.979][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 14:50:00.982][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[E][2025-04-02 +80 15:27:28.566][31209, 1*][main][daemon][FLog_MainBinderManager][][onServiceDisconnected,processFlag:daemon
[I][2025-04-02 +80 15:27:28.566][31209, 1*][main][daemon][IPCMonitor][][onServiceDisconnected daemon
[W][2025-04-02 +80 15:27:28.569][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:27:28.570][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: report freeze. diffMs = 2243967 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 2253967 , taskStartElapsedRealtimeMs = 428233629 , reportEventTime = 430487596
[I][2025-04-02 +80 15:27:28.570][31209, 69][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: duration = 2253967 , taskStartDate = 20250402 , currentDate = 20250402
[I][2025-04-02 +80 15:27:28.570][31209, 69][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: [20250402]-2253967 , durationMs = 2253967
[I][2025-04-02 +80 15:27:28.590][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=87
[I][2025-04-02 +80 15:27:28.591][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=87
[I][2025-04-02 +80 15:27:28.591][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=87
[I][2025-04-02 +80 15:27:28.594][31209, 36][temporary-1][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-02 +80 15:27:28.596][31209, 47][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 15:27:28.600][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=87
[I][2025-04-02 +80 15:27:28.600][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=87
[I][2025-04-02 +80 15:27:28.600][31209, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=87
[I][2025-04-02 +80 15:27:28.606][31209, 69][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:27:28.607][31209, 69][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:27:28.612][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250402 , nowCount = 0 , lastExecuteTime = 1743576534307
[I][2025-04-02 +80 15:27:28.612][31209, 69][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-02 +80 15:27:28.620][31209, 43][temporary-4][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[I][2025-04-02 +80 15:27:28.627][31209, 47][temporary-8][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 15:27:28.627][31209, 47][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [14131秒]
[I][2025-04-02 +80 15:27:28.641][31209, 43][temporary-4][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[I][2025-04-02 +80 15:27:28.642][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:27:28.642][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:27:28.645][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:27:28.646][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:27:28.646][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:27:28.647][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 15:27:28.655][31209, 47][temporary-8][daemon][CmdMetrics][][all        1        100.0        95       未探测          {}                  
[I][2025-04-02 +80 15:27:28.655][31209, 47][temporary-8][daemon][CmdMetrics][][stat       1        100.0        95       未探测          {}                  
[I][2025-04-02 +80 15:27:28.735][31209, 46][temporary-7][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 15:27:28.735][31209, 46][temporary-7][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 15:27:28.751][31209, 46][temporary-7][daemon][LogTunnel][][processUAXXX 1
[W][2025-04-02 +80 15:27:28.859][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:27:28.861][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:27:41.075][31209, 36][temporary-1][daemon][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 15:27:41.836][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:27:41.839][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 15:30:00.899][31209, 218][LogProcessService-7][daemon][jimxia][][jimxia, get appCaller: 13 get appVia:UNKNOWN_VIA
[I][2025-04-02 +80 15:30:00.902][31209, 218][LogProcessService-7][daemon][HttpNetWorkTaskV2][][[509]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:509,useHttp2:false
[I][2025-04-02 +80 15:30:00.928][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[509]callStart
[I][2025-04-02 +80 15:30:00.929][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[509]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 15:30:00.932][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[509]connectStart
[I][2025-04-02 +80 15:32:41.019][31209, 43][temporary-4][daemon][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 15:32:41.088][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:32:41.097][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 15:32:41.098][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 15:32:41.100][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 15:32:41.100][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 15:32:41.101][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 15:32:41.102][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 15:32:42.590][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 15:32:42.593][31209, 213][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 15:32:42.869][31209, 1*][main][daemon][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-02 +80 15:51:38.596][31209, 69][alive_report][daemon][AliveFreezeCheckTask][][executeTask: report freeze. diffMs = 1439986 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 1449986 , taskStartElapsedRealtimeMs = 430487655 , reportEventTime = 431937641
[I][2025-04-02 +80 15:51:38.596][31209, 69][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: duration = 1449986 , taskStartDate = 20250402 , currentDate = 20250402
[I][2025-04-02 +80 15:51:38.596][31209, 69][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: [20250402]-1449986 , durationMs = 1449986
[W][2025-04-02 +80 15:51:38.612][31209, 213][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 15:51:38.621][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[509]connectFailed protocol:null
[I][2025-04-02 +80 15:51:38.621][31209, 478][protocalManagerStatReport-478][daemon][HttpNetWorkTaskV2][][[509]dnsStart domainName:mayybstatnew.3g.qq.com
