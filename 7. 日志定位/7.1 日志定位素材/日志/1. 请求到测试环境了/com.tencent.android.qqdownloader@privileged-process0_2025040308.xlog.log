~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 20:12:34.851][12573, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[22045,22239][2025-04-03 +0800 08:03:07]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[22045,22239][2025-04-03 +0800 08:03:07]
get mmap time: 56
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:0 free:0 available:0
log dir space info, capacity:0 free:0 available:0
[I][2025-04-03 +80 08:03:07.986][22045, 40][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-03 +80 08:03:07.993][22045, 40][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:03:07.994][22045, 40][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-03 +80 08:03:07.994][22045, 40][temporary-2][cache][unknown][Settings][NameValueCache: false
[I][2025-04-03 +80 08:03:07.994][22045, 40][temporary-2][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-03 +80 08:03:07.994][22045, 40][temporary-2][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-03 +80 08:03:07.994][22045, 40][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=false, collectImei=false}
[E][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][RightlySDKManager][initSDK time =108
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:03:07.996][22045, 40][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[E][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigValue: RDelivery尚未初始化. key = privacy_info_report_config
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][RdefenseInitTask][init
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:03:07.997][22045, 40][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:03:08.000][22045, 40][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 08:03:08.000][22045, 40][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-03 +80 08:03:08.000][22045, 40][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:03:08.000][22045, 40][temporary-2][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 08:03:08.000][22045, 40][temporary-2][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-03 +80 08:03:08.000][22045, 40][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-03 +80 08:03:08.000][22045, 40][temporary-2][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-03 +80 08:03:08.000][22045, 40][temporary-2][cache][unknown][WxApiWrapper][init wxAppSupportApi:671101502
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][mBinderManagerConnection -> onServiceConnected
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][RDeliverySetting][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][queryBinder, binderCode : 1, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][addService, service : true
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][addService, service : true
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][MMKVInitiator][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][addService, service : true
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][BinderManager][addService, service : true
[E][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][ActivityThreadHacker][ApplicationThread$Stub fields is null
[E][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][Monitor][Monitor init fail. key = service_monitor
[E][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][Monitor][Monitor init fail. key = receiver_monitor
[E][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][Monitor][Monitor init fail. key = activity_monitor
[E][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][Monitor][Monitor init fail. key = process_monitor
[I][2025-04-03 +80 08:03:08.001][22045, 40][temporary-2][cache][unknown][ActivityThreadHacker][start Hook successful, dur = 279
[I][2025-04-03 +80 08:03:08.040][22045, 40][temporary-2][unknown][AstApp][][xlog init finished:unknown
[I][2025-04-03 +80 08:03:08.110][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 513, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 08:03:08.127][22045, 57][StartThread-57][unknown][report_tag][][hookAms = false
[I][2025-04-03 +80 08:03:08.297][22045, 57][StartThread-57][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:03:08.297][22045, 57][StartThread-57][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 08:03:08.311][22045, 57][StartThread-57][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-03 +80 08:03:08.319][22045, 57][StartThread-57][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:03:08.319][22045, 57][StartThread-57][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-03 +80 08:03:12.208][22045, 38][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922819.ea0.xc.e(ProGuard:82)
	at yyb8922819.ea0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922819.ga0.xb$xb.c(ProGuard:52)
	at yyb8922819.l90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-03 +80 08:03:12.434][22045, 42][temporary-4][unknown][RdefenseIdleTask][][init
[I][2025-04-03 +80 08:03:12.454][22045, 42][temporary-4][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-03 +80 08:03:14.416][22045, 52][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 08:03:14.417][22045, 52][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-03 +80 08:03:14.425][22045, 52][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:03:14.426][22045, 52][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 08:03:14.432][22045, 52][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-03 +80 08:03:14.473][22045, 98][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-03 +80 08:03:19.465][22045, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-03 +80 08:06:09.569][22045, 1*][main][unknown][FLog_MainBinderManager][][onServiceDisconnected,processFlag:unknown
[I][2025-04-03 +80 08:06:09.578][22045, 41][temporary-3][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-03 +80 08:06:09.594][22045, 41][temporary-3][unknown][MainBinderManager][][tryToConnect process:unknown
[E][2025-04-03 +80 08:09:20.637][22045, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:09:20.866][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:09:21.452][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:09:39.256][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:09:39.975][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:09:50.268][22045, 29][Binder:22045_3][unknown][STGlobal][][syncAppCallerFromDeamon start STGlobal.appCaller = 1, AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:09:50.276][22045, 29][Binder:22045_3][unknown][STGlobal][][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-03 +80 08:09:50.277][22045, 29][Binder:22045_3][unknown][STGlobal][][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:09:50.278][22045, 29][Binder:22045_3][unknown][STGlobal][][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-03 +80 08:09:50.278][22045, 29][Binder:22045_3][unknown][STGlobal][][syncAppCallerFromDeamon start STGlobal.appCaller = 13, AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:09:50.282][22045, 29][Binder:22045_3][unknown][STGlobal][][syncAppCallerFromDeamon end STGlobal.appCaller = 13
[I][2025-04-03 +80 08:09:50.282][22045, 29][Binder:22045_3][unknown][STGlobal][][syncAppViaFromDeamon start STGlobal.appVia = UNKNOWN_VIA, AstApp.getProcessFlag() = unknown
[I][2025-04-03 +80 08:09:50.283][22045, 29][Binder:22045_3][unknown][STGlobal][][syncAppViaFromDeamon end STGlobal.appVia = UNKNOWN_VIA
[I][2025-04-03 +80 08:10:16.778][22045, 41][temporary-3][unknown][GdtAdSdkInitTask][][Not main or daemon process
[I][2025-04-03 +80 08:11:02.046][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:11:03.773][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:13:03.022][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:13:50.032][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:13:50.117][22045, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-03 +80 08:13:50.516][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:13:52.650][22045, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 08:15:43.850][22045, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 08:17:49.266][22045, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:17:49.343][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:17:49.835][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:19:43.345][22045, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:19:43.388][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:19:44.530][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:20:10.620][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:20:10.963][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:20:55.999][22045, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:20:56.037][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:20:56.628][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:22:24.770][22045, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[E][2025-04-03 +80 08:23:56.183][22045, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:23:56.198][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:23:56.910][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:23:57.067][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-03 +80 08:25:07.063][22045, 1*][main][unknown][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-03 +80 08:25:07.402][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-03 +80 08:25:31.406][22045, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-03 +80 08:30:11.936][22045, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ begin of mmap ~~~~~
[I][2025-04-03 +80 08:37:40.408][22045, 60][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
~~~~~ end of mmap ~~~~~[17830,17955][2025-04-03 +0800 08:53:21]
