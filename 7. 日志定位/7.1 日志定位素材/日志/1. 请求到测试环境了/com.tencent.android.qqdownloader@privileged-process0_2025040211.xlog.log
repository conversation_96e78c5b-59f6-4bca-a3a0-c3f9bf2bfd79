~~~~~ begin of mmap ~~~~~
[I][2025-03-31 +80 20:44:15.526][16650, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
~~~~~ end of mmap ~~~~~[30286,30484][2025-04-02 +0800 11:31:21]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[30286,30484][2025-04-02 +0800 11:31:21]
get mmap time: 43
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77034774528 available:76900556800
log dir space info, capacity:117409054720 free:76879581184 available:76879581184
~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 11:31:21.282][30286, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-02 +80 11:31:21.282][30286, 39][temporary-2][cache][unknown][MMKVInitiator][need prepare mmkv, from: MMKVWrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 11:31:21.282][30286, 39][temporary-2][cache][unknown][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-02 +80 11:31:21.282][30286, 39][temporary-2][cache][unknown][Settings][NameValueCache: false
[I][2025-04-02 +80 11:31:21.282][30286, 39][temporary-2][cache][unknown][Settings][getString key:has_show_protocol:[true]
[I][2025-04-02 +80 11:31:21.282][30286, 39][temporary-2][cache][unknown][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-02 +80 11:31:21.282][30286, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-02 +80 11:31:21.282][30286, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-02 +80 11:31:21.282][30286, 39][temporary-2][cache][unknown][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922671.cd.j@18cf52a, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2671', isDaemonProcess=false, collectImei=false}
[E][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][RightlySDKManager][initSDK time =349
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][RightlySDKManager][setAllowPolicy: true
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][initConfig: config = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk, false
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~B4FGmURPXT_CHRKhMex_-A==/com.tencent.android.qqdownloader-cBHSSAT6NK_U4AfPfLJZwg==/base.apk
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][report: {signType=V2}
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelIdManager][return NA
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon start STGlobal.appCaller = 6, AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][STGlobal][syncAppCallerFromDeamon end STGlobal.appCaller = 1
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon start STGlobal.appVia = , AstApp.getProcessFlag() = unknown
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][STGlobal][syncAppViaFromDeamon end STGlobal.appVia = 
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][XLog][init, proccess:unknown
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelIdManager][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][RdefenseInitTask][init
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][BinderManager][connectToServiceOptimize
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][RDeliveryProvider_TAG][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][cache][unknown][FLog_qqlive_log][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-02 +80 11:31:21.283][30286, 39][temporary-2][unknown][AstApp][][xlog init finished:unknown
[I][2025-04-02 +80 11:31:21.284][30286, 44][temporary-7][unknown][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-02 +80 11:31:21.296][30286, 61][io_thread][unknown][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 11:31:21.298][30286, 40][temporary-3][unknown][WxApiWrapper][][init wxAppSupportApi:671101502
[I][2025-04-02 +80 11:31:21.333][30286, 65][io_thread][unknown][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-02 +80 11:31:21.340][30286, 56][StartThread-56][unknown][MMKVInitiator][][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-02 +80 11:31:21.341][30286, 56][StartThread-56][unknown][MMKVInitiator][][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[E][2025-04-02 +80 11:31:21.341][30286, 43][temporary-6][unknown][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[I][2025-04-02 +80 11:31:21.342][30286, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-02 +80 11:31:21.345][30286, 1*][main][unknown][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-02 +80 11:31:21.348][30286, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:31:21.348][30286, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 11:31:21.350][30286, 46][SendEventDispatcher][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:31:21.351][30286, 36][temporary-1][unknown][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 11:31:21.361][30286, 46][SendEventDispatcher][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:31:21.363][30286, 42][temporary-5][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : onIPCConnected, binder:true
[I][2025-04-02 +80 11:31:21.368][30286, 41][temporary-4][unknown][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:31:21.369][30286, 36][temporary-1][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:31:21.408][30286, 65][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 59, dataMap.size = 321, memSize = 325.408203125
[I][2025-04-02 +80 11:31:21.436][30286, 41][temporary-4][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:31:21.448][30286, 42][temporary-5][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:31:21.449][30286, 61][io_thread][unknown][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 128, dataMap.size = 321, memSize = 325.408203125
[E][2025-04-02 +80 11:31:21.600][30286, 43][temporary-6][unknown][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-02 +80 11:31:21.601][30286, 43][temporary-6][unknown][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-02 +80 11:31:21.601][30286, 43][temporary-6][unknown][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-02 +80 11:31:21.601][30286, 43][temporary-6][unknown][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-02 +80 11:31:21.669][30286, 43][temporary-6][unknown][ActivityThreadHacker][][start Hook successful, dur = 439
[I][2025-04-02 +80 11:31:21.780][30286, 56][StartThread-56][unknown][MainBinderManager][][tryToConnect process:unknown
[I][2025-04-02 +80 11:31:21.801][30286, 1*][main][unknown][FLog_MainBinderManager][][[main]onServiceConnected,processFlag:unknown
server:android.os.BinderProxy@8da17c
[I][2025-04-02 +80 11:31:21.827][30286, 56][StartThread-56][unknown][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:true
[I][2025-04-02 +80 11:31:21.827][30286, 56][StartThread-56][unknown][MainBinderManager][][addServiceOptimize, service true
[I][2025-04-02 +80 11:31:21.902][30286, 56][StartThread-56][unknown][report_tag][][hookAms = false
[I][2025-04-02 +80 11:31:21.976][30286, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:31:21.976][30286, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 11:31:21.979][30286, 56][StartThread-56][unknown][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-02 +80 11:31:21.982][30286, 56][StartThread-56][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:31:21.984][30286, 56][StartThread-56][unknown][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-02 +80 11:31:25.852][30286, 37][PrimaryMonitorNetwork][unknown][RightlySDKManager_PandoraEx.ReportBaseInfo][][Initialization failed
java.lang.IllegalArgumentException: Invalid path: /data
	at android.os.StatFs.doStat(StatFs.java:53)
	at android.os.StatFs.<init>(StatFs.java:43)
	at android.database.sqlite.SQLiteGlobal.getDefaultPageSize(SQLiteGlobal.java:78)
	at android.database.sqlite.SQLiteConnection.setPageSize(SQLiteConnection.java:315)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:272)
	at android.database.sqlite.SQLiteConnection.open(SQLiteConnection.java:219)
	at android.database.sqlite.SQLiteConnectionPool.openConnectionLocked(SQLiteConnectionPool.java:559)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:222)
	at android.database.sqlite.SQLiteConnectionPool.open(SQLiteConnectionPool.java:211)
	at android.database.sqlite.SQLiteDatabase.openInner(SQLiteDatabase.java:951)
	at android.database.sqlite.SQLiteDatabase.open(SQLiteDatabase.java:930)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:794)
	at android.database.sqlite.SQLiteDatabase.openDatabase(SQLiteDatabase.java:783)
	at android.database.sqlite.SQLiteOpenHelper.getDatabaseLocked(SQLiteOpenHelper.java:388)
	at android.database.sqlite.SQLiteOpenHelper.getWritableDatabase(SQLiteOpenHelper.java:331)
	at yyb8922671.ha0.xc.e(ProGuard:82)
	at yyb8922671.ha0.xc$xb.a(ProGuard:45)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper.<init>(ProGuard:36)
	at com.tencent.qmethod.monitor.report.base.db.DBHelper$xb.a(ProGuard:32)
	at yyb8922671.ja0.xb$xb.c(ProGuard:52)
	at yyb8922671.o90.xb.l(ProGuard:304)
	at com.tencent.qmethod.monitor.config.ConfigManager$xd.run(ProGuard:184)
	at android.os.Handler.handleCallback(Handler.java:966)
	at android.os.Handler.dispatchMessage(Handler.java:110)
	at android.os.Looper.loopOnce(Looper.java:205)
	at android.os.Looper.loop(Looper.java:293)
	at android.os.HandlerThread.run(HandlerThread.java:110)
Caused by: android.system.ErrnoException: statvfs failed: EPERM (Operation not permitted)
	at libcore.io.Linux.statvfs(Native Method)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at libcore.io.BlockGuardOs.statvfs(BlockGuardOs.java:427)
	at libcore.io.ForwardingOs.statvfs(ForwardingOs.java:859)
	at android.system.Os.statvfs(Os.java:924)
	at android.os.StatFs.doStat(StatFs.java:51)
	... 26 more
[I][2025-04-02 +80 11:31:26.157][30286, 43][temporary-6][unknown][RdefenseIdleTask][][init
[I][2025-04-02 +80 11:31:26.173][30286, 43][temporary-6][unknown][CMNetworkCallbackHook][][source map is empty
[I][2025-04-02 +80 11:31:28.079][30286, 51][BeaconReportHandler][unknown][BinderManager][][queryBinder, binderCode : 121, mBinderManager = true, source : getService, binder:true
[I][2025-04-02 +80 11:31:28.080][30286, 51][BeaconReportHandler][unknown][BinderManager][][addService, service : true
[I][2025-04-02 +80 11:31:28.096][30286, 51][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:31:28.097][30286, 51][BeaconReportHandler][unknown][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 11:31:28.104][30286, 51][BeaconReportHandler][unknown][QimeiManager][][updateQimeiProtocolPermission use cache
[I][2025-04-02 +80 11:31:28.140][30286, 100][oaid_thread_0][unknown][QimeiManager][][onResult supprt: true, aaid: , oaid: ef3ad442-3609-463b-b63b-ac8c1cbc7df2
[I][2025-04-02 +80 11:31:33.129][30286, 1*][main][unknown][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[E][2025-04-02 +80 11:31:53.064][30286, 1*][main][unknown][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
~~~~~ end of mmap ~~~~~[31737,31937][2025-04-02 +0800 11:32:02]
