[I][2025-04-02 +80 20:28:38.816][11932, 210][vrpool-1-thread][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:28:38.817][11932, 210][vrpool-1-thread][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:28:53.142][11932, 1*][main][market][CloudPhoneNotificationReceiver][][onDisconnected called()
[I][2025-04-02 +80 20:29:00.017][11932, 53][temporary-6][market][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:29:00.025][11932, 37][temporary-1][market][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:29:18.301][11932, 51][temporary-4][market][RdeliveryConfigFetcher][][canRefreshRDelivery remain:10460576
[I][2025-04-02 +80 20:29:18.311][11932, 51][temporary-4][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:29:18.311][11932, 51][temporary-4][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:29:18.706][11932, 603][OkHttp https://rdelivery.qq.com/...][market][RDeliveryProvider_TAG][][成功更新RDelivery配置 guid:2058663700530012608， mGuid:2058663700530012608
[I][2025-04-02 +80 20:29:18.707][11932, 603][OkHttp https://rdelivery.qq.com/...][market][RdeliveryConfigFetcher][][Rdelivery refresh configure success!
[I][2025-04-02 +80 20:29:19.715][11932, 39][temporary-2][market][BookingPreDownCalendar][][no permission
[I][2025-04-02 +80 20:29:20.738][11932, 53][temporary-6][market][RDeliveryReportService][][reportFetchConfig: param:{report_event=fetch_suc, event_type=all, switch_value={"PassPhraseFeature.config.morePassPhraseRegex":"2343231#1","key_protocol_mgr_use_http_task_v2":"1_2322216#2","key_jce_cmd_enable_custom_dns":"1_2322222#2","key_enable_phantom_plugin":"true_2304056#1","key_forbid_set_wallpaper_config":"_2328575#1","fix_webview_input_softInput_urls":"2003958#3","key_enable_download_show_storage_dialog":"true_1728211#1","key_ionia_report_app_pkg_list":"_2318091#4","key_is_home_page_staggered_grid_preload_enabled":"false_2272450#1","key_special_work_to_vivo_android_14":"true_2126990#4","check_download_flow_v2":"1237448#7","key_enable_ionia_using_v3_SWITCH":"true_1897609#1","key_booking_pre_file_expired_duration_com.tencent.tmgp.sgame":"3_1733624#1","enablePreloadSearchResultPhotonPage":"false_1073381#4","key_use_photon_kuikly_place_holder_switch":"true_2217964#1","Key_bo_ms_switch":"true_2062718#1","key_splashview_adapt_to_foldable_screens":"true_2270291#1","KuiklyFeature.switch.enableKuiklyHttpFix":"false_1740594#1","enable_pre_create_video_view":"false_1620861#1","HomePageCommonFeature.switch.enablePhotonRuntimeFragmentClickToRefresh":"true_1663782#1","key_frequency_day_guide_second_play_limit":"3_1079923#1","key_image_face_detection_switch":"true_1567010#2","key_use_desktop_window_guid_black_list":"false_1897055#1","key_create_plugin_download_info_synchronized":"true_836006#1","key_install_session_game_go_back_original":"true_1918848#1","key_system_install_avoid_repeat_install":"false_1616436#1","HomePageCommonFeature.switch.enableHomeTabAnchorFreqControl":"true_1725868#1","key_new_assistant_page":"true_2195229#1","enable_sync_videoview":"false_1468388#1","recent_use_mini_program_widget_margin":"60_876344#2","key_page_prepare_data_p75":"true_1199985#1","key_dual_user_not_pause_on_wifi_lost":"true_1779643#1","key_ionia_enable_abort_detect_v2":"false_2244218#2","key_install_jump_other_page":"true_1274730#2","PassPhraseFeature.config.longPassPhraseRegex":"^.*?\/~([^~\\s]+)~\/.*$_2289153#1","key_qd_downloader_switch_url_types":"|1|2|_1839333#1","key_install_session_auto_open_list":"1246030#9","key_calculate_black_pixel_count":"false_878065#1","key_ai_face_photo_switch":"true_1569055#2","key_enable_check_channel_switch":"false_2194478#1","enable_report_photon_page_static_v2":"1169816#1","BookingFeature.switch.isBookingDialogImmutable":"false_1671354#2","key_enable_necessary_pop_in_download_activity":"true_1814995#1","enable_report_preload_kuikly_dex":"false_2026653#1","key_honor_push_enable":"true_1576901#1","enable_thread_switch_on_update_data":"false_1432932#2","key_open_system_event_report":"true_527253#1","key_kuikly_pc_game_new_game_bottom_direct_config2":"2272193#1","kuikly_page_resource_loader_request_interval":"30000_1737172#3","qr_black_url_list":"570356#2","key_use_desktop_window_behavior":"true_335140#1","key_install_retry_enable_screen_timer_check":"false_1939745#1","key_pkg_change_task_fix_enabled2":"false_1653695#1","key_ionia_enable_state_report":"false_2248354#3","key_use_desktop_window_guid_black_list_v2":"false_1897616#1","key_support_64_arch_plugin_list":"1529992#1","key_rdelivery_polling":"true_1586836#2","enable_start_before_on_attached":"false_1432830#2","BookingDownloaderFeature.switch.enableStorage":"true_1772929#1","cg_download_speed_type_config_1":"1284399#2","cg_download_speed_type_config":"1195730#2","config_qd_downloader_force_https_domain_list":"dd.myapp.com_2154788#2","enableHuyaVideoSdk":"true_2303956#1","key_desktop_window_exposure_fail_limit":"20_1135978#1","key_discover_do_init_task":"false_1971986#1","key_enable_session_install":"false_801663#1","key_ionia_vivo_video_strategy":"true_2170899#1","key_enable_recreate_toolbar_get_newdata":"true_1595900#4","key_rubbish_scan_regex":"false_2008621#1","key_ai_zone_center_page_bubble":"1596216#1","key_temperature_threshold":"{\"level1\":35,\"level2\":39}_1998145#1","key_ai_config_logout_when_ai_request_failed":"false_2072719#1","key_peak_cut_enable":"true_2313671#1","enable_thread_switch_optional":"false_1438144#1","config_bpd_calendar_remainder_only_report_found":"true_1956178#1","key_booking_down_task_chain_bind_min_progress":"15_1728619#1","key_homegame_tab_delay_render_p75":"true_1268353#4","key_enable_skip_record_push_req":"true_2096630#1","enable_panyu_test_1":"false_1522783#1","key_qd_downloader_enable_section_length_v2":"true_1753566#2","key_photon_common_cache_enable":"true_1748359#1","enable_fix_share_success_qq_report":"true_1173525#2","key_insert_new_ad_click_jump_logic":"true_1940312#1","key_discover_entrance_force_open":"false_1985702#2","key_enable_kr_download_text_size":"true_2189575#1","key_pag_lowest_android_version":"0_318135#1","key_cg_process_status_fix":"true_870197#1","enable_report_widget_add_state":"true_1173510#2","enablePreloadSearchResultPage":"false_1082043#5","key_install_session_jump_search":"true_1279200#5","key_td_log_open":"true_576499#2","support_minigame_android_version_switch":"false_1208192#2","keyEnableGetUserActivity":"true_1330795#1","key_network_traffic_config":"2043943#1","enable_wx_mini_program_hook_handler":"true_1167848#1","key_yyb_image_clean_compress_enabled":"true_1625140#2","key_enable_ionia_starter_plugin":"true_495381#1","BookingPreDown.Feature.enableScreenOff":"true_1772919#1","key_widget_add_new_privilege":"true_671242#3","key_qd_downloader_disable_dual_down":"false_1687309#2","enable_report_oaid_and_android_id":"false_1556913#2","key_desktop_window_use_launcher_visible_event":"2016_535399#1","down_load_page_app_cloud_game_style":"true_1567130#4","key_cloud_disk_preview_black_list":"mkv_2276540#1","key_middle_page_welfare_game_download_layer_display":"true_2172227#1","key_enable_log_upload_in_FAQ":"true_439954#1","key_exit_guid_of_game_page":"true_440456#1","key_discover_slide_video_show_cover":"true_2183032#1","key_enable_daemon_start_res_monitor":"true_2305227#1","key_fix_app_detail_auto_download_fail_switch":"true_2006175#1","enable_optimize_search_init_page":"true_846241#4","show_install_tips":"false_309175#2","key_accessibility_enable":"false_756097#1","constellation_widget_solution_type":"4_873864#1","key_ai_zone_center_page_floatball":"1596203#2","key_enable_ionia_starter_plugin_v3":"true_1897613#1","key_phantom_plugin_app_config":"2340006#1","ai_zone_no_need_login":"true_1733685#2","key_wx_miniprogram_max_shortcut_prompts_per_day":"2_774872#1","key_forbbid_request_private_storage_permission":"true_1937652#1","BookingDownloaderFeature.switch.channelPkgEnableDual":"false_1762275#1","key_enable_merge_apk_report":"true_1728878#1","constellation_widget_margin":"60_876346#2","key_photon_card_preload":"true_1775558#1","key_update_booking_download":"true_1788492#3","key_install_fail_extra_report":"true_1949456#1","top_header_view_alpha_transparent_switch":"false_1956425#1","key_direct_jump_main_enable":"false_633206#1","key_app_details_official_desc_tencent_download":"2_2093567#1","DynamicSplash.switch.enableSplashPriority":"false_1548535#2","key_peak_cut_delay_time":"3000_2313676#1","key_download_notification_flag":"2_1125723#1","key_qd_downloader_expand_transports_num_54153350":"5_2219333#1","key_interrupt_float_layer_show":"1671990#2","KEY_BACKUP_WECHAT_MANUAL":"true_2084667#1","config_bpd_calendar_enable":"true_1965718#1","kuikly_key_region_config_v2":"1725689#1","key_replace_channel_support_reflect":"true_1656736#4","key_ionia_brand_display_black_List":"none_501249#3","key_get_device_name_switch":"true_319431#2","key_desktop_window_2016_skip_use_launcher_visible":"true_2096626#1","key_other_app_clean_desk_msg_data_permission":"true_1956629#1","enable_avid_un_shell_update_auto_download":"true_1505139#2","key_enable_game_tab_preload_anchor":"true_1725671#1","key_cmd_dual_enable":"_2328528#2","BookingFeature.switch.enableBookingButtonWelfareState":"false_1671485#1","key_cloud_disk_app_backup":"true_2316882#1","key_ka_res_load_switch":"false_1870881#1","key_ionia_strategy_StartByAllowListTokenStrategy":"true_2296501#1","BookingDownloaderFeature.config.bookingPreSvrFlowStateUpdateIntervalSecs":"30_1736367#1","enable_report_shell_download_time":"true_1173521#2","key_rdelivery_disable_anti_freezing":"false_1516310#2","key_kuikly_pc_game_new_game_bottom_direct_config":"_2262304#1","key_wxqq_clean_cloud_disk_entrance_switch":"true_2135599#1","config_bpd_calendar_app_data_54418333":"2140858#2","BookingDownloaderFeature.config.defaultClientPullDataIntevalTime":"3600_1737063#1","key_enable_minigame_info_report":"true_882685#1","key_has_desktop_window_behavior_permission":"true_335141#1","key_normal_splash_add_video_kind":"true_2310549#1","enable_create_constellation_widget":"true_914064#2","key_ka_invoke_max_times":"-1_1870895#1","key_ionia_enable_abort_detect":"false_1890742#1","mechanized_popup_request_frequency_switch":"true_2263474#1","key_new_photo_clean_scan":"true_1160413#7","photon_home_page_engine_add_photon_version_switch":"true_2089715#1","is_hw_aware_open_gray":"false_1657494#1","enable_delay_shell_download":"false_1877371#1","BookingDownloaderFeature.switch.disableOldBookingRequest":"true_1788374#1","enable_dump_king_card_state":"true_1057369#1","key_new_plugin_loading_page":"false_758387#1","key_enable_install_with_allow_permission_for_allow_brand_rdelivery":"false_827377#1","key_pag_switch":"true_415054#1","key_discover_recommend_guide_config":"0,2,-1,-1,-1_1917231#2","key_desktop_window_open_float_window":"true_335142#1","enable_report_king_card_state":"false_1036209#1","key_patch_support_format":"800_2272241#1","key_enable_ionia_starter_plugin_v2":"true_1890828#1","key_ionia_brand_display_black_List_v2":"google,tcl,blackberry_1881716#3","key_start_trail_force_download_info":"_780248#1","key_middle_page_welfare_new_user_card":"true_2179112#1","key_enable_custom_service":"true_739737#1","is_hw_aware_open_872":"false_1746603#1","AutoDownloadFeature.switch.enableAutoDownloadOnMobileNetwork":"true_1748458#1","BookingDownloaderFeature.config.reportTaskIntevalTime":"1_1722175#1","key_dual_new_guide":"true_1872336#1","key_welfare_login_style":"true_1840667#3","key_request_full_rate_duration":"108000_1562934#1","key_discover_default_tab_position":"2_2235089#1","key_sp_never_process_work_on_main_thread":"true_613809#1","key_enable_phantom_all_app_on_white_list":"false_1560885#1","key_ionia_compat_phantom_version":"160_1620833#1","key_cg_quick_start_adjust_enable":"true_1607748#5","key_vivo_use_adb_install":"false_765677#1","BookingDownloaderFeature.switch.enableAppBackground":"true_1772933#1","plugin_oom_release_context":"false_1609772#1","key_float_on_op":"false_488328#2","enable_pop_window_when_splash_hidden":"true_1556467#1","key_score_config":"2118928#1","key_enable_kr_report_element_exposure":"true_2189576#1","kuikly_key_region_config":"1720768#1","key_app_clean_pkg_names":"2298727#1","test_config":"0_329100#1","key_enable_hook_base_context_res":"true_2313633#1","key_book_calendar_config":"2140173#1","BookingDownloaderFeature.switch.enableBattery":"true_1772928#1","key_init_cloud_disk_transfer_task_when_yyb_launch":"true_2043817#2","BookingDownloaderFeature.switch.enableRunningTask":"true_1772938#1","BookingPreDown.Feature.enableAppBackground":"true_1772918#1","key_aigc_wallpaper_prebuilt_delay":"3000_2047615#2","traffic_default_max_download_size":"200_1781762#2","key_enable_kr_app_stub_text_size":"true_2189573#1","key_patch_enable_strategy":"true_2272243#1","qd_downloader_save_buff_full_size":"100_2203976#1","key_pure_enhance_mode_check":"true_2181550#1","key_enable_report_app":"_1265305#1","key_plugin_update_threshold":"300000_493574#1","key_qd_downloader_enable_expand_transports":"true_1753544#2","key_middle_page_dialog_game_download_layer_display":"true_2181096#1","key_enable_phantom_all_staging_page":"true_1548514#2","key_enable_qd_downloader_log":"true_1509568#2","key_rdelivery_clean_result_style":"1_1655382#1","key_new_traffic_dialog_exp1":"true_1951125#1","key_discover_video_recycle_force_stop":"false_2027508#1","config_bpd_calendar_app_data_54229156":"2066983#1","key_enable_android_data_bypass":"true_1980041#1","config_bpd_calendar_app_list":"54418333_2043461#2","key_pkg_change_task_fix_enabled":"false_1622698#1","BookingDownloaderFeature.switch.enableScreenOff":"true_1772934#1","key_enable_middle_view_model_fix_9":"false_1643036#1","key_enable_download_speed_zero":"true_822438#1","key_enable_ionia_dismiss_delay":"false_1745413#1","BookingPreDown.Feature.enableBattery":"true_1772911#1","key_qd_downloader_enable_trans_control_v2":"true_1753538#2","enable_king_card_toast_style":"true_1951594#1","key_enable_freeze_frame_report":"true_591415#4","enable_max_download_number":"1_2193641#2","key_disable_start_patch_on_launch":"true_2272231#1","key_bk_on_ruok":"true_482812#9","key_browser_h5_auto_download":"false_1303626#3","key_process_alive_looper_duration":"3600000_537925#3","key_kuikly_preload_page_config":"2250068#1","key_enable_mechanize_popup_home":"true_2263475#1","key_tab_double_image_config_enable":"true_1728572#1","check_download_flow":"true_1165724#1","key_install_session_jump_config":"_1522876#2","key_enable_new_no_wifi_download_dialog":"true_1781751#2","key_ionia_strategy_StartByAccountServiceStrategy":"true_2268923#4","key_ionia_notify_media_session":"true_2295177#2","kuikly_ABTest_config":"2277300#3","key_low_latency_config":"31_519366#2","file_format_open_map":"1738536#1","key_dual_user_do_not_show_traffic_dialog":"true_1704360#3","BookingDownloaderFeature.switch.channelPkgEnable5GDirect":"false_1762276#1","key_get_bluetooth_name_switch":"true_319453#2","key_game_id_enable_sr":"755369#3","key_video_clean_cloud_disk_entrance_switch":"true_2135981#1","key_rdelivery_fix_honor_huawei_push":"true_1711907#1","key_is_first_page_cache_enable":"false_522344#3","key_peak_cut_hour_config":"0;7;8;15_2313675#1","key_ka_res_request_interval":"604800000_1876242#1","key_qd_downloader_enable_switch_url":"true_1597895#1","enable_check_version_before_load_video_plugin":"true_1528078#1","config_cmd_enable_net_probe":"1_2266956#1","key_qd_downloader_enable_check_cost_time_fix":"1_2275327#2","BookingPreDown.Feature.enableStorage":"true_1772916#1","key_desktop_black_list_using_v2_key":"true_1897598#1","key_sp_never_waiting_finish_queue":"true_613803#1","key_allow_cp_query_yyb_info_apps":"1808386#2","key_big_file_clean_cloud_disk_entrance_switch":"true_2135958#1","key_install_retry_enable_retry_task":"true_1897552#1","key_qd_downloader_expand_transports_num":"2_2202908#1","welfare_should_award_point":"false_1630205#1","key_hw_aware_model_config":"1703593#1","key_qd_downloader_enable_queue_v2":"true_1699835#1","enable_optimize_search_result_page":"true_846260#7","DynamicSplash.switch.enableDynamicSplash":"true_1548531#7","key_bk_max_ver":"13.0.36.0_493943#1","key_desktop_beacon_report_rate_rule":"true_490679#1","key_downloading_float_show_min_size_mb":"300_778181#1","enable_download_version_code_suffix_white_list":"2080007#4","key_other_app_clean_config":"1704269#1","key_enable_topview_show_bubble_when_downloaded":"true_1708318#2","key_enable_sr":"true_755386#2","key_installer_config":"1894619#1","key_enable_necessary_window_v2":"true_1789611#1","key_feedback_tel_phone":"4006%20700%20700_1435735#1","key_outer_download_succ_launch_config":"{}_2317944#1","key_ea_dex_class_loader_switch":"true_2062739#1","key_0927_enable_codec_detecti
[I][2025-04-02 +80 20:29:38.817][11932, 211][vrpool-2-thread][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:29:38.817][11932, 211][vrpool-2-thread][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:30:00.023][11932, 51][temporary-4][market][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:30:00.025][11932, 42][temporary-3][market][FLog_login_log][][[temporary-3]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:30:24.060][11932, 1*][main][market][CloudDiskAutoBackupManager][][startAllAutoBackupIfNeed
[I][2025-04-02 +80 20:30:24.060][11932, 1*][main][market][CloudDiskAutoBackupManager][][startAlbumBackupIfNeed
[I][2025-04-02 +80 20:30:24.060][11932, 1*][main][market][CloudDiskAutoBackupManager][][checkAllowBackup
[W][2025-04-02 +80 20:30:24.061][11932, 1*][main][market][CloudDiskAutoBackupManager][][checkAllowBackup return: permission
[I][2025-04-02 +80 20:30:24.061][11932, 1*][main][market][CloudDiskAutoBackupManager][][#startWechatBackupIfNeed
[I][2025-04-02 +80 20:30:24.061][11932, 1*][main][market][CloudDiskAutoBackupManager][][checkAllowBackup
[W][2025-04-02 +80 20:30:24.062][11932, 1*][main][market][CloudDiskAutoBackupManager][][checkAllowBackup return: permission
[I][2025-04-02 +80 20:30:24.192][11932, 1*][main][market][FLog_onPause][][[main]SettingActivity,
[I][2025-04-02 +80 20:30:24.194][11932, 1*][main][market][ProcessLifecycleManager][][onActivityPaused, activity: com.tencent.nucleus.manager.setting.SettingActivity@accda01
[I][2025-04-02 +80 20:30:24.201][11932, 1*][main][market][BaseActivity][][[ActivityLifeCycle] onPause com.tencent.nucleus.manager.setting.SettingActivity
[I][2025-04-02 +80 20:30:24.251][11932, 1*][main][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:30:24.251][11932, 1*][main][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:30:24.283][11932, 1*][main][market][FLog_onStop][][[main]MainActivity,
[I][2025-04-02 +80 20:30:24.284][11932, 1*][main][market][ProcessLifecycleManager][][onActivityStopped, activity: com.tencent.assistantv2.activity.MainActivity@94fefe1
[I][2025-04-02 +80 20:30:24.284][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ onActivityStopped activity name :MainActivity
[I][2025-04-02 +80 20:30:24.284][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ updateActivityVisibleCount mActivityVisibleCount=1
[I][2025-04-02 +80 20:30:24.284][11932, 76][proc_lifecycle_proxy][market][yyb8922819.j2.xd][][ getItem  from active key = 1, fragment = xb{5a0a790} (ef751226-229a-4cc4-b0c7-05f188122f25 id=0x7f080a3f tag=android:switcher:2131233343:1)
[I][2025-04-02 +80 20:30:24.289][11932, 1*][main][market][BaseActivity][][[ActivityLifeCycle] onStop com.tencent.assistantv2.activity.MainActivity
[I][2025-04-02 +80 20:30:24.290][11932, 1*][main][market][VideoViewComponent_DiscoverRecommend_10843_139699436][][onStop() called with: context = [com.tencent.assistantv2.activity.MainActivity@94fefe1], this: com.tencent.assistant.component.video.view.VideoViewComponentV2{853a4ec VFE...C.. ......I. 0,0-0,0}
[I][2025-04-02 +80 20:30:24.290][11932, 1*][main][market][playerViewVisibleChanged_0][][【onStop】visiblei: false playerViewVisible: false isHuyaVideo: false url: null
[I][2025-04-02 +80 20:30:24.291][11932, 1*][main][market][playerViewVisibleChanged_0][][【backHome】visiblei: false playerViewVisible: false isHuyaVideo: false url: null
[I][2025-04-02 +80 20:30:24.297][11932, 1*][main][market][VideoViewComponent_DiscoverRecommend_10843_139699436][][onStop, playerViewStateChangeListener: null needHandlerBySelf: true
[I][2025-04-02 +80 20:30:24.298][11932, 1*][main][market][assistant][][tryStopPlay return,  isStopped: false, isInitPlayStatus: true
[I][2025-04-02 +80 20:30:24.298][11932, 1*][main][market][VideoViewComponent_DiscoverRecommend_10843_73786143][][onStop() called with: context = [com.tencent.assistantv2.activity.MainActivity@94fefe1], this: com.tencent.assistant.component.video.view.VideoViewComponentV2{465e31f VFE...C.. ........ 539,1169-540,1170}
[I][2025-04-02 +80 20:30:24.299][11932, 1*][main][market][VideoViewComponent_DiscoverRecommend_10843_73786143][][onStop, playerViewStateChangeListener: null needHandlerBySelf: true
[I][2025-04-02 +80 20:30:24.299][11932, 1*][main][market][assistant][][tryStopPlay return,  isStopped: true, isInitPlayStatus: false
[I][2025-04-02 +80 20:30:24.299][11932, 1*][main][market][tag_LaunchSpeed][][[main],[LaunchTagger][1]clear tags in market process.
[I][2025-04-02 +80 20:30:24.308][11932, 1*][main][market][FLog_onStop][][[main]SettingActivity,
[I][2025-04-02 +80 20:30:24.309][11932, 1*][main][market][ProcessLifecycleManager][][onActivityStopped, activity: com.tencent.nucleus.manager.setting.SettingActivity@accda01
[I][2025-04-02 +80 20:30:24.309][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ onActivityStopped activity name :SettingActivity
[I][2025-04-02 +80 20:30:24.309][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ updateActivityVisibleCount mActivityVisibleCount=0
[I][2025-04-02 +80 20:30:24.314][11932, 1*][main][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:30:24.314][11932, 1*][main][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:30:24.321][11932, 1*][main][market][BaseActivity][][[ActivityLifeCycle] onStop com.tencent.nucleus.manager.setting.SettingActivity
[I][2025-04-02 +80 20:30:24.330][11932, 210][vrpool-1-thread][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:30:24.330][11932, 210][vrpool-1-thread][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:30:24.363][11932, 39][temporary-2][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:30:24.364][11932, 39][temporary-2][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:30:24.735][11932, 1*][main][market][GlobalMonitor][][app go background, process:market
[I][2025-04-02 +80 20:30:24.735][11932, 1*][main][market][download-floating-window][][receive UI_EVENT_APP_GOBACKGROUND
[I][2025-04-02 +80 20:30:24.735][11932, 1*][main][market][PassPhraseManager][][App go background
[I][2025-04-02 +80 20:30:24.736][11932, 1*][main][market][StatusBarEventController][][#handleUIEvent: msg.what=1039
[I][2025-04-02 +80 20:30:24.736][11932, 1*][main][market][StatusBarEventController][][#handleOther: what=1039
[I][2025-04-02 +80 20:30:24.736][11932, 1*][main][market][StorageOptimizeService][][app go background
[I][2025-04-02 +80 20:30:24.749][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:30:24.750][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:30:24.752][11932, 37][temporary-1][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:30:24.753][11932, 37][temporary-1][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:30:24.753][11932, 37][temporary-1][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:30:24.753][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 20:30:24.767][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200765 endTime:1743609600765
[I][2025-04-02 +80 20:30:24.768][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:30:24.769][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:30:24.774][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:30:24.774][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 20:30:25.472][11932, 54][temporary-7][market][FLog_login_log][][[temporary-7]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:30:25.472][11932, 54][temporary-7][market][FLog_login_log][][[temporary-7]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:30:25.745][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:30:25.745][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:30:25.748][11932, 53][temporary-6][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:30:25.748][11932, 53][temporary-6][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:30:25.749][11932, 53][temporary-6][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:30:25.749][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 20:30:25.751][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200751 endTime:1743609600751
[I][2025-04-02 +80 20:30:25.752][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:30:25.752][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:30:25.758][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:30:25.758][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[E][2025-04-02 +80 20:30:26.393][11932, 189][TVK-Scheduled-1][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[E][2025-04-02 +80 20:30:29.434][11932, 190][TVK-Scheduled-2][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[I][2025-04-02 +80 20:30:29.815][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[I][2025-04-02 +80 20:30:30.799][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[E][2025-04-02 +80 20:30:32.434][11932, 189][TVK-Scheduled-1][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[E][2025-04-02 +80 20:30:35.433][11932, 189][TVK-Scheduled-1][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[E][2025-04-02 +80 20:30:35.495][11932, 1*][main][market][FLog_MainBinderManager][][onServiceDisconnected,processFlag:market
[I][2025-04-02 +80 20:30:35.498][11932, 52][kcsdk_temporary-5][market][MainBinderManager][][tryToConnect process:market
[I][2025-04-02 +80 20:30:35.761][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:30:35.761][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:30:35.762][11932, 37][temporary-1][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:30:35.771][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200771 endTime:1743609600771
[I][2025-04-02 +80 20:30:35.772][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:30:35.772][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:30:35.774][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:30:35.774][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 20:30:35.775][11932, 37][temporary-1][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:30:35.775][11932, 37][temporary-1][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:30:35.776][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 20:30:44.087][11932, 53][temporary-6][market][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:30:44.092][11932, 52][kcsdk_temporary-5][market][FLog_login_log][][[kcsdk_temporary-5]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[E][2025-04-02 +80 20:30:44.137][11932, 189][TVK-Scheduled-1][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[I][2025-04-02 +80 20:30:44.467][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:30:44.468][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:30:44.468][11932, 42][temporary-3][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:30:44.471][11932, 42][temporary-3][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:30:44.472][11932, 42][temporary-3][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:30:44.473][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 20:30:44.481][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200480 endTime:1743609600481
[I][2025-04-02 +80 20:30:44.482][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:30:44.484][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:30:44.506][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:30:44.507][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 20:30:46.479][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[E][2025-04-02 +80 20:30:47.099][11932, 190][TVK-Scheduled-2][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[I][2025-04-02 +80 20:30:49.519][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[E][2025-04-02 +80 20:30:50.134][11932, 189][TVK-Scheduled-1][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[E][2025-04-02 +80 20:30:53.134][11932, 189][TVK-Scheduled-1][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[E][2025-04-02 +80 20:30:56.133][11932, 189][TVK-Scheduled-1][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[I][2025-04-02 +80 20:30:56.953][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:30:56.953][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:30:56.954][11932, 37][temporary-1][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:30:56.961][11932, 37][temporary-1][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:30:56.965][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200964 endTime:1743609600965
[I][2025-04-02 +80 20:30:56.966][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:30:56.966][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:30:56.967][11932, 37][temporary-1][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:30:56.968][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:30:56.968][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 20:30:56.974][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[E][2025-04-02 +80 20:30:59.137][11932, 189][TVK-Scheduled-1][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[I][2025-04-02 +80 20:57:04.080][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[W][2025-04-02 +80 20:57:04.158][11932, 90][halley_2800_TempTask][market][halley-cloud-AccessIpMgr][][onNetworkSwitch refresh ScheduleInfo:unknown,netType:0
[I][2025-04-02 +80 20:57:04.244][11932, 53][temporary-6][market][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:57:04.326][11932, 52][kcsdk_temporary-5][market][FLog_login_log][][[kcsdk_temporary-5]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:57:04.437][11932, 51][temporary-4][market][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:57:04.437][11932, 39][temporary-2][market][FLog_login_log][][[temporary-2]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:57:04.438][11932, 42][temporary-3][market][FLog_login_log][][[temporary-3]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:57:04.438][11932, 54][temporary-7][market][FLog_login_log][][[temporary-7]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:57:04.441][11932, 1*][main][market][SystemEventManager][][apn changed: WIFI -> NO_NETWORK, notifyDisconnected, from:BroadcastReceiver
[I][2025-04-02 +80 20:57:04.449][11932, 52][kcsdk_temporary-5][market][DataBaseContextWrapper][][[galtest] dbPath=/data/user/0/com.tencent.android.qqdownloader/files/database/mobile_media.db
[I][2025-04-02 +80 20:57:04.479][11932, 1*][main][market][CloudDiskUploadEngineImpl][][onStatusChanged type=NETWORK resumedDownload=false
[I][2025-04-02 +80 20:57:04.479][11932, 1*][main][market][CloudDiskDownloadEngineImpl][][onStatusChanged type=NETWORK resumedDownload=false
[I][2025-04-02 +80 20:57:04.480][11932, 253][DefaultDispatcher-worker-8][market][CloudDiskUploadEngineImpl][][pauseTask
[I][2025-04-02 +80 20:57:04.480][11932, 253][DefaultDispatcher-worker-8][market][CloudDiskDownloadEngineImpl][][pauseTask
[I][2025-04-02 +80 20:57:04.485][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:57:04.486][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:57:04.490][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200490 endTime:1743609600490
[I][2025-04-02 +80 20:57:04.491][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:57:04.492][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:57:04.493][11932, 54][temporary-7][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:57:04.494][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:57:04.494][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 20:57:04.497][11932, 54][temporary-7][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:57:04.497][11932, 54][temporary-7][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:57:04.500][11932, 1*][main][market][FLog_qqlive_log][][[main]PluginLoadQualityReporter:onDownloadPause

[I][2025-04-02 +80 20:57:04.500][11932, 1*][main][market][FLog_qqlive_log][][[main]PluginLoadObserver:UI_EVENT_PLUGIN_DOWNLOAD_PAUSE :com.tencent.assistant.plugin.video_64

[I][2025-04-02 +80 20:57:04.525][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:04.537][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:04.544][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:04.556][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:04.563][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:04.572][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 20:57:05.022][11932, 1*][main][market][KingCardUsersUndertakeManager][][showKingCardHintIfNeed return: isKingCard
[E][2025-04-02 +80 20:57:07.149][11932, 190][TVK-Scheduled-2][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[I][2025-04-02 +80 20:57:07.242][11932, 37][temporary-1][market][KingCardPluginManager][][大王卡状态变更：4
[I][2025-04-02 +80 20:57:07.260][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:07.261][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:07.262][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:07.263][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:07.263][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:07.786][11932, 1*][main][market][KingCardUsersUndertakeManager][][showKingCardHintIfNeed return: isKingCard
[I][2025-04-02 +80 20:57:09.498][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[I][2025-04-02 +80 20:57:10.614][11932, 51][temporary-4][market][GetAppUpdateEntranceManager][][sendRequest
[I][2025-04-02 +80 20:57:10.634][11932, 1*][main][market][SystemEventManager][][apn changed: NO_NETWORK -> WIFI, notifyConnected, from:BroadcastReceiver
[I][2025-04-02 +80 20:57:10.648][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:10.649][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:10.649][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:10.650][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:10.650][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:10.685][11932, 52][kcsdk_temporary-5][market][HomeBaseFragment][][HomeSecondFloorMultiTabFragment: onConnected
[I][2025-04-02 +80 20:57:10.701][11932, 1*][main][market][DownloadTag][][startAllWaitingForMobileNetworkDownloadTask: true true
[I][2025-04-02 +80 20:57:10.704][11932, 52][kcsdk_temporary-5][market][GDTDM.GlobalAmsDownloadManager][][onConnected, apn: WIFI
[I][2025-04-02 +80 20:57:10.705][11932, 1*][main][market][GDTDM.GlobalAmsDownloadManager][][notify onWifiConnected. entries: 0
[I][2025-04-02 +80 20:57:10.711][11932, 52][kcsdk_temporary-5][market][HomeBaseFragment][][xb: onConnected
[I][2025-04-02 +80 20:57:10.752][11932, 52][kcsdk_temporary-5][market][CloudDiskUploadEngineImpl][][onStatusChanged type=NETWORK resumedDownload=true
[I][2025-04-02 +80 20:57:10.752][11932, 52][kcsdk_temporary-5][market][CloudDiskDownloadEngineImpl][][onStatusChanged type=NETWORK resumedDownload=true
[I][2025-04-02 +80 20:57:10.752][11932, 250][DefaultDispatcher-worker-5][market][CloudDiskUploadEngineImpl][][resumedTask
[I][2025-04-02 +80 20:57:10.754][11932, 1*][main][market][miles][][加载字体<nuclear>耗时：24
[E][2025-04-02 +80 20:57:10.780][11932, 51][temporary-4][market][CkCoreManager][][wrapper is not valid, bid:40, version:4, frequency:10800000, hasResource:true, checkTime:1743403384674
[I][2025-04-02 +80 20:57:10.814][11932, 250][DefaultDispatcher-worker-5][market][CloudDiskUploadEngineImpl][][resumedTask
[I][2025-04-02 +80 20:57:10.815][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:57:10.815][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:57:10.820][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200820 endTime:1743609600820
[I][2025-04-02 +80 20:57:10.821][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:57:10.821][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:57:10.823][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:57:10.823][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 20:57:10.824][11932, 52][kcsdk_temporary-5][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:57:10.825][11932, 52][kcsdk_temporary-5][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:57:10.825][11932, 52][kcsdk_temporary-5][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:57:10.948][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 20:57:11.192][11932, 1*][main][market][KingCardUsersUndertakeManager][][showKingCardHintIfNeed return: isKingCard
[I][2025-04-02 +80 20:57:11.733][11932, 295][PluginDownDelayHandler][market][report_tag][][success ---> doPreDownload. sceneId=0
[W][2025-04-02 +80 20:57:11.736][11932, 295][PluginDownDelayHandler][market][report_tag][][not need pre down config, ignore increase update: PluginDownloadInfo{pluginId=-1996238048, downloadTicket='com.tencent.assistant.plugin.phantom_64', pluginPackageName='com.tencent.assistant.plugin.phantom', version=104, name='Phantom组件', desc='Phantom组件', displayOrder=0, iconUrl='', imgUrl='', fileSize=276015, minApiLevel=21, minPluginVersion=70, minBaoVersion=8484130, needPreDownload=1, downUrl='https://shiply-yyb-1258344701.file.myqcloud.com/reshub/yyb/com.tencent.assistant.plugin.phantom_64/formal/20250310204232/production/plugin_phantom-arm64-v8a-104-20250310204216.plg', startActivity='', type=1, sceneId='0', priority='50', tacticsId='null', buildNo=1234, publishTime=1741610536549, publishType=2, updateType=11, netType='1;2;3;4;5', status=1, updateTime=1741610536549, cmd=0, digest='FBE8DDC120942C0C9D18C71A606A4A8F1EA41866', resHubTaskId = 2304034}
[I][2025-04-02 +80 20:57:11.736][11932, 295][PluginDownDelayHandler][market][report_tag][][*** before adjustListSortByPkgName ***
[I][2025-04-02 +80 20:57:11.736][11932, 295][PluginDownDelayHandler][market][report_tag][][*** after adjustListSortByPkgName ***
[I][2025-04-02 +80 20:57:11.736][11932, 295][PluginDownDelayHandler][market][report_tag][][doPreDownload : 没有插件要预下载！！！
[I][2025-04-02 +80 20:57:13.994][11932, 52][kcsdk_temporary-5][market][KingCardPluginManager][][大王卡状态变更：4
[I][2025-04-02 +80 20:57:14.007][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:14.008][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:14.009][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:14.010][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:14.010][11932, 1*][main][market][AppStubComponentImpl][][#handleUIEvent: msg.what=1316, msg.obj=null
[I][2025-04-02 +80 20:57:14.532][11932, 1*][main][market][KingCardUsersUndertakeManager][][showKingCardHintIfNeed return: isKingCard
[I][2025-04-02 +80 20:57:15.389][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:57:15.390][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:57:15.394][11932, 51][temporary-4][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:57:15.397][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200396 endTime:1743609600397
[I][2025-04-02 +80 20:57:15.398][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:57:15.399][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:57:15.401][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:57:15.401][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 20:57:15.402][11932, 51][temporary-4][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:57:15.402][11932, 51][temporary-4][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:57:15.402][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 20:57:15.863][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[I][2025-04-02 +80 20:57:16.434][11932, 1*][main][market][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 20:58:55.118][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[I][2025-04-02 +80 20:58:55.149][11932, 53][temporary-6][market][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:58:55.149][11932, 53][temporary-6][market][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:58:55.150][11932, 53][temporary-6][market][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:58:55.151][11932, 54][temporary-7][market][FLog_login_log][][[temporary-7]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:58:55.476][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:58:55.477][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:58:55.477][11932, 52][kcsdk_temporary-5][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:58:55.485][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200485 endTime:1743609600485
[I][2025-04-02 +80 20:58:55.487][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:58:55.488][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:58:55.498][11932, 52][kcsdk_temporary-5][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:58:55.499][11932, 52][kcsdk_temporary-5][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:58:55.502][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:58:55.502][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 20:58:55.507][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 20:59:00.050][11932, 54][temporary-7][market][FLog_login_log][][[temporary-7]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:59:00.051][11932, 39][temporary-2][market][FLog_login_log][][[temporary-2]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:59:00.196][11932, 1*][main][market][FLog_onStart][][[main]MainActivity,
[I][2025-04-02 +80 20:59:00.198][11932, 1*][main][market][ProcessLifecycleManager][][onActivityStarted, activity: com.tencent.assistantv2.activity.MainActivity@94fefe1
[I][2025-04-02 +80 20:59:00.198][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ onActivityStarted activity name :MainActivity
[I][2025-04-02 +80 20:59:00.198][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ updateActivityVisibleCount prev=0,mActivityVisibleCount=1
[I][2025-04-02 +80 20:59:00.198][11932, 1*][main][market][AppStartReport][][reportStartEvent: start.
[I][2025-04-02 +80 20:59:00.200][11932, 76][proc_lifecycle_proxy][market][yyb8922819.j2.xd][][ getItem  from active key = 1, fragment = xb{5a0a790} (ef751226-229a-4cc4-b0c7-05f188122f25 id=0x7f080a3f tag=android:switcher:2131233343:1)
[I][2025-04-02 +80 20:59:00.204][11932, 1*][main][market][BaseActivity][][[ActivityLifeCycle] onStart com.tencent.assistantv2.activity.MainActivity
[I][2025-04-02 +80 20:59:00.248][11932, 1*][main][market][FLog_onStart][][[main]SettingActivity,
[I][2025-04-02 +80 20:59:00.249][11932, 1*][main][market][ProcessLifecycleManager][][onActivityStarted, activity: com.tencent.nucleus.manager.setting.SettingActivity@accda01
[I][2025-04-02 +80 20:59:00.249][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ onActivityStarted activity name :SettingActivity
[I][2025-04-02 +80 20:59:00.249][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ updateActivityVisibleCount prev=1,mActivityVisibleCount=2
[I][2025-04-02 +80 20:59:00.250][11932, 1*][main][market][BaseActivity][][[ActivityLifeCycle] onStart com.tencent.nucleus.manager.setting.SettingActivity
[I][2025-04-02 +80 20:59:00.252][11932, 1*][main][market][FLog_onResume][][[main]SettingActivity,
[I][2025-04-02 +80 20:59:00.253][11932, 1*][main][market][ProcessLifecycleManager][][onActivityResumed, activity: com.tencent.nucleus.manager.setting.SettingActivity@accda01
[I][2025-04-02 +80 20:59:00.253][11932, 1*][main][market][ProcessLifecycleManager][][notifyActivityResumed: activity = com.tencent.nucleus.manager.setting.SettingActivity@accda01 , booleanExtra = false , mPauseTempForegroundActivity = java.lang.ref.WeakReference@a98b0f6
[I][2025-04-02 +80 20:59:00.259][11932, 1*][main][market][BaseActivity][][[ActivityLifeCycle] onResume com.tencent.nucleus.manager.setting.SettingActivity
[I][2025-04-02 +80 20:59:00.259][11932, 1*][main][market][BaseActivity][][onResume: delay showFloatingBall, showDelayMs = 1000
[I][2025-04-02 +80 20:59:00.259][11932, 1*][main][market][STPageEventsInfo][][autoTestOnPageEnter: pageId = [2035], pageHeight = [0]
[I][2025-04-02 +80 20:59:00.260][11932, 210][vrpool-1-thread][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:00.260][11932, 210][vrpool-1-thread][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:59:00.262][11932, 1*][main][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.262][11932, 1*][main][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.264][11932, 53][temporary-6][market][SelfUpdateManager][][isNeedShowDialog mSelfUpdateInfo:false
[I][2025-04-02 +80 20:59:00.265][11932, 1*][main][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.265][11932, 1*][main][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.268][11932, 1*][main][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.268][11932, 1*][main][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.271][11932, 52][kcsdk_temporary-5][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.271][11932, 52][kcsdk_temporary-5][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.272][11932, 1*][main][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.272][11932, 1*][main][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.272][11932, 37][temporary-1][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.272][11932, 37][temporary-1][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.272][11932, 54][temporary-7][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.272][11932, 54][temporary-7][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.278][11932, 42][temporary-3][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.279][11932, 42][temporary-3][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.287][11932, 39][temporary-2][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.287][11932, 39][temporary-2][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.288][11932, 37][temporary-1][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.288][11932, 37][temporary-1][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.292][11932, 1*][main][market][DualDownloadApkManager][][handleUIEvent { when=-2d15h8m20s566ms realwhen=0 isVsync=false isAsynchronous=false what=1519 target=com.tencent.assistant.event.EventDispatcher$xc }
[I][2025-04-02 +80 20:59:00.292][11932, 1*][main][market][DualDownloadApkManager][][sim false
[I][2025-04-02 +80 20:59:00.293][11932, 1*][main][market][PassPhraseManager][][App go visible
[I][2025-04-02 +80 20:59:00.293][11932, 1*][main][market][DualDownloadApkManager][][handleUIEvent { when=-2d15h8m20s567ms realwhen=0 isVsync=false isAsynchronous=false what=1519 target=com.tencent.assistant.event.EventDispatcher$xc }
[I][2025-04-02 +80 20:59:00.293][11932, 1*][main][market][DualDownloadApkManager][][sim false
[I][2025-04-02 +80 20:59:00.293][11932, 1*][main][market][GlobalMonitor][][app go foreground, process:market
[I][2025-04-02 +80 20:59:00.293][11932, 1*][main][market][download-floating-window][][receive UI_EVENT_APP_GOFRONT
[I][2025-04-02 +80 20:59:00.294][11932, 1*][main][market][InstallRetryMgr][][retryOneTask from:onGoFront, isUnlockScreen:false
[I][2025-04-02 +80 20:59:00.294][11932, 1*][main][market][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-02 +80 20:59:00.294][11932, 1*][main][market][StatusBarEventController][][#handleUIEvent: msg.what=1040
[I][2025-04-02 +80 20:59:00.294][11932, 1*][main][market][StatusBarEventController][][#handleOther: what=1040
[I][2025-04-02 +80 20:59:00.294][11932, 1*][main][market][StorageOptimizeService][][app go foreground
[I][2025-04-02 +80 20:59:00.302][11932, 53][temporary-6][market][DownloadTag][][traceId:0 msg:startAllWaitingForWifiDownloadTask()
[I][2025-04-02 +80 20:59:00.303][11932, 52][kcsdk_temporary-5][market][RdeliveryConfigFetcher][][refreshRdeliveryConfigure remain:-1482001, count:1， maxCount:2
[I][2025-04-02 +80 20:59:00.306][11932, 52][kcsdk_temporary-5][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:00.307][11932, 52][kcsdk_temporary-5][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:59:00.391][11932, 1*][main][market][SelfBackgroundReceiver][][<receiver> received user present action lastCheckTime=1743588563550
[I][2025-04-02 +80 20:59:00.506][11932, 108][DefaultHandler][market][PreUpdateAppEngine][][tryAutoDownload PreUpdateAppEngine 没有需要更新的数据
[I][2025-04-02 +80 20:59:00.702][11932, 39][temporary-2][market][AppStartReport][][reportStartEvent: real start.
[I][2025-04-02 +80 20:59:00.703][11932, 39][temporary-2][market][AppStartReport][][ reportCachetStartEvent cacheUri =  ， lastCacheUri =null 
[I][2025-04-02 +80 20:59:00.703][11932, 39][temporary-2][market][AppStartReport][][ reportCachetStartEvent forwardUri=null , forwardReportUri = null
[I][2025-04-02 +80 20:59:00.703][11932, 39][temporary-2][market][AppStartReport][][ reportCachetStartEvent: extendFields = {page_id=2035, start_type=1, is_new_install=3}
[I][2025-04-02 +80 20:59:00.711][11932, 39][temporary-2][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.712][11932, 39][temporary-2][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.721][11932, 39][temporary-2][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:00.721][11932, 39][temporary-2][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:00.763][11932, 53][temporary-6][market][InstallQueue~][][send release lock msg when app resume, delay time is：500
[I][2025-04-02 +80 20:59:00.763][11932, 53][temporary-6][market][InstallUninstallTask][][ cancelSystemInstallTimer 
[I][2025-04-02 +80 20:59:00.764][11932, 53][temporary-6][market][InstallUninstallTask][][ startSystemInstallTimer right now 
[I][2025-04-02 +80 20:59:00.764][11932, 108][DefaultHandler][market][InstallQueue~][][[appResume] runnable notify SystemInstallThread and release lock~
[I][2025-04-02 +80 20:59:00.764][11932, 53][temporary-6][market][InstallQueue~][][onAppResume, check install queue isEmpty=true, lastProceedElapse=1743598740764, inInstalling=false, isAppAtFront=true
[I][2025-04-02 +80 20:59:00.764][11932, 108][DefaultHandler][market][InstallQueue~][][notify install thread~ task left size=0
[I][2025-04-02 +80 20:59:00.764][11932, 108][DefaultHandler][market][InstallQueue~][][setInstalling=false
[I][2025-04-02 +80 20:59:00.764][11932, 108][DefaultHandler][market][InstallUninstallTask][][updateLastNotifyTime = 1743598740764
[I][2025-04-02 +80 20:59:00.793][11932, 1*][main][market][PassPhraseManager][][onFirstActivityStart, appGoVisible
[I][2025-04-02 +80 20:59:00.793][11932, 1*][main][market][PassPhraseManager][][readPassPhraseFromClip: triggerReason = appGoVisible
[I][2025-04-02 +80 20:59:00.796][11932, 1*][main][market][Profiler][][[PassPhraseProfiler]  >> Start: ReadPassPhrase#3 <<
[I][2025-04-02 +80 20:59:00.797][11932, 1*][main][market][PassPhraseManager][][readPassPhraseFromClip: not outer call.
[I][2025-04-02 +80 20:59:00.799][11932, 1*][main][market][PassPhraseManager][][readPassPhraseFromClip: canShowPair.first = true , canShowPair.second = OK
[I][2025-04-02 +80 20:59:00.799][11932, 1*][main][market][PassPhraseManager][][readPassPhraseFromClip: Workflow start
[I][2025-04-02 +80 20:59:00.800][11932, 622][coroutine-bg-622][market][PassPhraseManager][][readPassPhraseFromClip: execute start.
[I][2025-04-02 +80 20:59:00.800][11932, 622][coroutine-bg-622][market][PassPhraseWorkflow][][execute: called.
[I][2025-04-02 +80 20:59:00.800][11932, 622][coroutine-bg-622][market][PassPhraseWorkflow][][initWorkflow: executor = com.tencent.assistantv2.passphrase.PassPhraseWorkflow$NormalExecutor@4418138
[I][2025-04-02 +80 20:59:00.804][11932, 1*][main][market][PassPhraseWorkflow][][work: start = ReadFromClipboardJob , input = 
[I][2025-04-02 +80 20:59:00.804][11932, 1*][main][market][PassPhraseWorkflow][][work: ReadFromClipboardJob , read primary clip.
[I][2025-04-02 +80 20:59:00.805][11932, 618][coroutine-bg-618][market][Profiler][][[PassPhraseProfiler] onEvent: >> OuterCallCheck << #1 in segment ReadPassPhrase#3
Since ReadPassPhrase#3 Start: 349,479 ns (1 ms);  Since Last: 0 ns (0 ms).
is_outer_call = false
[W][2025-04-02 +80 20:59:00.806][11932, 1*][main][market][PassPhraseWorkflow][][work: ReadFromClipboardJob , primary clip rawData empty.
[I][2025-04-02 +80 20:59:00.807][11932, 622][coroutine-bg-622][market][PassPhraseManager][][readPassPhraseFromClip: execute result = Fail(ErrorCode: -1, ErrorMessage: ClipEmpty)
[W][2025-04-02 +80 20:59:00.807][11932, 622][coroutine-bg-622][market][PassPhraseManager][][readFromClipboard failed: Fail(ErrorCode: -1, ErrorMessage: ClipEmpty)
[I][2025-04-02 +80 20:59:00.808][11932, 619][coroutine-bg-619][market][Profiler][][[PassPhraseProfiler] onEvent: >> ReadPassPhrase << #1 in segment None
Since ReadPassPhrase#3 Start: -2,456,771 ns (-2 ms);  Since Last: -2,456,771 ns (-2 ms).
reason = appGoVisible
[I][2025-04-02 +80 20:59:00.811][11932, 622][coroutine-bg-622][market][Profiler][][[PassPhraseProfiler]  >> End: ReadPassPhrase#3 << 
Since ReadPassPhrase#3 Start: 12,032,813 ns (12 ms);  Since Last: 661,980 ns (0 ms).
[I][2025-04-02 +80 20:59:00.814][11932, 624][coroutine-bg-624][market][Profiler][][[PassPhraseProfiler] onEvent: >> CheckPassPhrase << #1 in segment ReadPassPhrase#3
Since None Start: 10,254,688 ns (11 ms);  Since Last: -1,778,125 ns (-1 ms).
is_success = false,
error_msg = ClipEmpty,
error_code = -1,
toast_msg = null,
ret = 0
[I][2025-04-02 +80 20:59:00.815][11932, 625][coroutine-bg-625][market][Profiler][][[PassPhraseProfiler] onEvent: >> ProcessFinish << #1 in segment ReadPassPhrase#3
Since ReadPassPhrase#3 Start: 11,370,833 ns (12 ms);  Since Last: 0 ns (0 ms).
reason = CheckPassPhrase failed, errorMessage: ClipEmpty
[I][2025-04-02 +80 20:59:00.875][11932, 620][OkHttp https://rdelivery.qq.com/...][market][RDeliveryProvider_TAG][][成功更新RDelivery配置 guid:2058663700530012608， mGuid:2058663700530012608
[I][2025-04-02 +80 20:59:00.878][11932, 620][OkHttp https://rdelivery.qq.com/...][market][RdeliveryConfigFetcher][][Rdelivery refresh configure success!
[I][2025-04-02 +80 20:59:00.914][11932, 1*][main][market][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 20:59:00.917][11932, 1*][main][market][UpdateRedDotExpManager][][#getRedDotCount: experimentGroup=0, redDotCount=13, totalSize=13
[I][2025-04-02 +80 20:59:00.917][11932, 1*][main][market][UpdateRedDotExpManager][][#getBubbleIconData: experimentGroup=0, size=13
[I][2025-04-02 +80 20:59:00.918][11932, 1*][main][market][app_manage_combine_card][][#showBubble: itemCount=13, bubbleCount=13, displayContent=13款应用可更新
[I][2025-04-02 +80 20:59:00.918][11932, 1*][main][market][TXImageView][][updateImageView: http://pp.myapp.com/ma_icon/0/icon_5848_1743491221/256, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.919][11932, 1*][main][market][TXImageView][][updateImageView: http://pp.myapp.com/ma_icon/0/icon_42350811_1743491870/256, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.919][11932, 1*][main][market][TXImageView][][updateImageView: http://pp.myapp.com/ma_icon/0/icon_10910_1743404843/256, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.920][11932, 1*][main][market][BookAppEngine][][getBookAppList: 6
[I][2025-04-02 +80 20:59:00.922][11932, 1*][main][market][StatusBarEventController][][#handleUIEvent: msg.what=1017
[I][2025-04-02 +80 20:59:00.922][11932, 1*][main][market][StatusBarEventController][][#handleOther: what=1017
[I][2025-04-02 +80 20:59:00.924][11932, 1*][main][market][NormalRecyclerViewAdapter][][updateKRRuntimeViewData isForceClear: true
[I][2025-04-02 +80 20:59:00.925][11932, 1*][main][market][TXImageView][][updateImageView: https://cms.myapp.com/wupload/xy/yybcms/iT3Oj5E2.png, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.927][11932, 1*][main][market][NormalRecyclerViewAdapter][][updateKRRuntimeViewData isForceClear: true
[I][2025-04-02 +80 20:59:00.927][11932, 1*][main][market][TXImageView][][updateImageView: https://cms.myapp.com/wupload/xy/yybcms/TYvkyg0b.png, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.929][11932, 1*][main][market][NormalRecyclerViewAdapter][][updateKRRuntimeViewData isForceClear: true
[I][2025-04-02 +80 20:59:00.929][11932, 1*][main][market][TXImageView][][updateImageView: https://cms.myapp.com/wupload/xy/yybcms/UAzlQInM.png, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.931][11932, 1*][main][market][NormalRecyclerViewAdapter][][updateKRRuntimeViewData isForceClear: true
[I][2025-04-02 +80 20:59:00.931][11932, 1*][main][market][TXImageView][][updateImageView: https://cms.myapp.com/wupload/xy/yybcms/Ewr9wNzo.png, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.937][11932, 29][Binder:11932_3][market][APPUpdate][][traceId:0 msg:notifyBussinessLayer|notifyWhat:1016
[I][2025-04-02 +80 20:59:00.944][11932, 1*][main][market][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 20:59:00.946][11932, 1*][main][market][redDot][][MgrTabRedDotManager notifyChange type 更新 msg.what = 1016
[I][2025-04-02 +80 20:59:00.949][11932, 1*][main][market][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 20:59:00.951][11932, 1*][main][market][UpdateRedDotExpManager][][#getRedDotCount: experimentGroup=0, redDotCount=13, totalSize=13
[I][2025-04-02 +80 20:59:00.951][11932, 1*][main][market][UpdateRedDotExpManager][][#getBubbleIconData: experimentGroup=0, size=13
[I][2025-04-02 +80 20:59:00.952][11932, 1*][main][market][app_manage_combine_card][][#showBubble: itemCount=13, bubbleCount=13, displayContent=13款应用可更新
[I][2025-04-02 +80 20:59:00.952][11932, 1*][main][market][TXImageView][][updateImageView: http://pp.myapp.com/ma_icon/0/icon_5848_1743491221/256, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.953][11932, 1*][main][market][TXImageView][][updateImageView: http://pp.myapp.com/ma_icon/0/icon_42350811_1743491870/256, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.953][11932, 1*][main][market][TXImageView][][updateImageView: http://pp.myapp.com/ma_icon/0/icon_10910_1743404843/256, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.954][11932, 1*][main][market][BookAppEngine][][getBookAppList: 6
[I][2025-04-02 +80 20:59:00.955][11932, 1*][main][market][StatusBarEventController][][#handleUIEvent: msg.what=1016
[I][2025-04-02 +80 20:59:00.955][11932, 1*][main][market][StatusBarEventController][][#handleOther: what=1016
[I][2025-04-02 +80 20:59:00.958][11932, 1*][main][market][NormalRecyclerViewAdapter][][updateKRRuntimeViewData isForceClear: true
[I][2025-04-02 +80 20:59:00.959][11932, 1*][main][market][TXImageView][][updateImageView: https://cms.myapp.com/wupload/xy/yybcms/iT3Oj5E2.png, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.960][11932, 1*][main][market][NormalRecyclerViewAdapter][][updateKRRuntimeViewData isForceClear: true
[I][2025-04-02 +80 20:59:00.960][11932, 1*][main][market][TXImageView][][updateImageView: https://cms.myapp.com/wupload/xy/yybcms/TYvkyg0b.png, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.962][11932, 1*][main][market][NormalRecyclerViewAdapter][][updateKRRuntimeViewData isForceClear: true
[I][2025-04-02 +80 20:59:00.962][11932, 1*][main][market][TXImageView][][updateImageView: https://cms.myapp.com/wupload/xy/yybcms/UAzlQInM.png, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:00.963][11932, 1*][main][market][NormalRecyclerViewAdapter][][updateKRRuntimeViewData isForceClear: true
[I][2025-04-02 +80 20:59:00.963][11932, 1*][main][market][TXImageView][][updateImageView: https://cms.myapp.com/wupload/xy/yybcms/Ewr9wNzo.png, imageType = NETWORK_IMAGE_ICON
[I][2025-04-02 +80 20:59:01.259][11932, 1*][main][market][BaseActivity][][onResume: delay showFloatingBall.
[I][2025-04-02 +80 20:59:01.263][11932, 1*][main][market][FakeFloatingBall][][getOrientation: context = com.tencent.nucleus.manager.setting.SettingActivity@accda01
[I][2025-04-02 +80 20:59:01.263][11932, 1*][main][market][FakeFloatingBall][][refreshScreenParams: screenWidthPixels = 1080 , screenHeightPixels = 2340
[I][2025-04-02 +80 20:59:01.286][11932, 1*][main][market][FLog_login_log][][[main]LoginProxy:id = com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849, processId = 3,needRefresh = false, currProcess = market
[I][2025-04-02 +80 20:59:01.296][11932, 53][temporary-6][market][InstallRetryMgr][][retryOneTask getNextRetryTask...
[I][2025-04-02 +80 20:59:01.464][11932, 53][temporary-6][market][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 20:59:01.493][11932, 53][temporary-6][market][AIGCRedDot][][getRedPotType isHasNewTask = false
[I][2025-04-02 +80 20:59:01.498][11932, 53][temporary-6][market][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 20:59:01.514][11932, 53][temporary-6][market][AIGCRedDot][][getRedPotType isHasNewTask = false
[I][2025-04-02 +80 20:59:01.520][11932, 53][temporary-6][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:01.520][11932, 53][temporary-6][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:01.521][11932, 53][temporary-6][market][redDot][][showRedDotWithTabType --- bundle = Bundle[{bubbleNumber=0, tabType=3, showRedDot=false, currentTabType=3}]
[I][2025-04-02 +80 20:59:01.535][11932, 51][temporary-4][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:01.535][11932, 51][temporary-4][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:01.772][11932, 1*][main][market][YYBUpdateDelayer][][#checkApkList: not start, first check
[I][2025-04-02 +80 20:59:01.879][11932, 37][temporary-1][market][BookingPreDownCalendar][][no permission
[I][2025-04-02 +80 20:59:02.287][11932, 54][temporary-7][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:02.288][11932, 54][temporary-7][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:02.296][11932, 54][temporary-7][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:02.296][11932, 54][temporary-7][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[E][2025-04-02 +80 20:59:02.544][11932, 1*][main][market][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-02 +80 20:59:02.880][11932, 53][temporary-6][market][RDeliveryReportService][][reportFetchConfig: param:{report_event=fetch_suc, event_type=all, switch_value={"PassPhraseFeature.config.morePassPhraseRegex":"2343231#1","key_protocol_mgr_use_http_task_v2":"1_2322216#2","key_jce_cmd_enable_custom_dns":"1_2322222#2","key_enable_phantom_plugin":"true_2304056#1","key_forbid_set_wallpaper_config":"_2328575#1","fix_webview_input_softInput_urls":"2003958#3","key_enable_download_show_storage_dialog":"true_1728211#1","key_ionia_report_app_pkg_list":"_2318091#4","key_is_home_page_staggered_grid_preload_enabled":"false_2272450#1","key_special_work_to_vivo_android_14":"true_2126990#4","check_download_flow_v2":"1237448#7","key_enable_ionia_using_v3_SWITCH":"true_1897609#1","key_booking_pre_file_expired_duration_com.tencent.tmgp.sgame":"3_1733624#1","enablePreloadSearchResultPhotonPage":"false_1073381#4","key_use_photon_kuikly_place_holder_switch":"true_2217964#1","Key_bo_ms_switch":"true_2062718#1","key_splashview_adapt_to_foldable_screens":"true_2270291#1","KuiklyFeature.switch.enableKuiklyHttpFix":"false_1740594#1","enable_pre_create_video_view":"false_1620861#1","HomePageCommonFeature.switch.enablePhotonRuntimeFragmentClickToRefresh":"true_1663782#1","key_frequency_day_guide_second_play_limit":"3_1079923#1","key_image_face_detection_switch":"true_1567010#2","key_use_desktop_window_guid_black_list":"false_1897055#1","key_create_plugin_download_info_synchronized":"true_836006#1","key_install_session_game_go_back_original":"true_1918848#1","key_system_install_avoid_repeat_install":"false_1616436#1","HomePageCommonFeature.switch.enableHomeTabAnchorFreqControl":"true_1725868#1","key_new_assistant_page":"true_2195229#1","enable_sync_videoview":"false_1468388#1","recent_use_mini_program_widget_margin":"60_876344#2","key_page_prepare_data_p75":"true_1199985#1","key_dual_user_not_pause_on_wifi_lost":"true_1779643#1","key_ionia_enable_abort_detect_v2":"false_2244218#2","key_install_jump_other_page":"true_1274730#2","PassPhraseFeature.config.longPassPhraseRegex":"^.*?\/~([^~\\s]+)~\/.*$_2289153#1","key_qd_downloader_switch_url_types":"|1|2|_1839333#1","key_install_session_auto_open_list":"1246030#9","key_calculate_black_pixel_count":"false_878065#1","key_ai_face_photo_switch":"true_1569055#2","key_enable_check_channel_switch":"false_2194478#1","enable_report_photon_page_static_v2":"1169816#1","BookingFeature.switch.isBookingDialogImmutable":"false_1671354#2","key_enable_necessary_pop_in_download_activity":"true_1814995#1","enable_report_preload_kuikly_dex":"false_2026653#1","key_honor_push_enable":"true_1576901#1","enable_thread_switch_on_update_data":"false_1432932#2","key_open_system_event_report":"true_527253#1","key_kuikly_pc_game_new_game_bottom_direct_config2":"2272193#1","kuikly_page_resource_loader_request_interval":"30000_1737172#3","qr_black_url_list":"570356#2","key_use_desktop_window_behavior":"true_335140#1","key_install_retry_enable_screen_timer_check":"false_1939745#1","key_pkg_change_task_fix_enabled2":"false_1653695#1","key_ionia_enable_state_report":"false_2248354#3","key_use_desktop_window_guid_black_list_v2":"false_1897616#1","key_support_64_arch_plugin_list":"1529992#1","key_rdelivery_polling":"true_1586836#2","enable_start_before_on_attached":"false_1432830#2","BookingDownloaderFeature.switch.enableStorage":"true_1772929#1","cg_download_speed_type_config_1":"1284399#2","cg_download_speed_type_config":"1195730#2","config_qd_downloader_force_https_domain_list":"dd.myapp.com_2154788#2","enableHuyaVideoSdk":"true_2303956#1","key_desktop_window_exposure_fail_limit":"20_1135978#1","key_discover_do_init_task":"false_1971986#1","key_enable_session_install":"false_801663#1","key_ionia_vivo_video_strategy":"true_2170899#1","key_enable_recreate_toolbar_get_newdata":"true_1595900#4","key_rubbish_scan_regex":"false_2008621#1","key_ai_zone_center_page_bubble":"1596216#1","key_temperature_threshold":"{\"level1\":35,\"level2\":39}_1998145#1","key_ai_config_logout_when_ai_request_failed":"false_2072719#1","key_peak_cut_enable":"true_2313671#1","enable_thread_switch_optional":"false_1438144#1","config_bpd_calendar_remainder_only_report_found":"true_1956178#1","key_booking_down_task_chain_bind_min_progress":"15_1728619#1","key_homegame_tab_delay_render_p75":"true_1268353#4","key_enable_skip_record_push_req":"true_2096630#1","enable_panyu_test_1":"false_1522783#1","key_qd_downloader_enable_section_length_v2":"true_1753566#2","key_photon_common_cache_enable":"true_1748359#1","enable_fix_share_success_qq_report":"true_1173525#2","key_insert_new_ad_click_jump_logic":"true_1940312#1","key_discover_entrance_force_open":"false_1985702#2","key_enable_kr_download_text_size":"true_2189575#1","key_pag_lowest_android_version":"0_318135#1","key_cg_process_status_fix":"true_870197#1","enable_report_widget_add_state":"true_1173510#2","enablePreloadSearchResultPage":"false_1082043#5","key_install_session_jump_search":"true_1279200#5","key_td_log_open":"true_576499#2","support_minigame_android_version_switch":"false_1208192#2","keyEnableGetUserActivity":"true_1330795#1","key_network_traffic_config":"2043943#1","enable_wx_mini_program_hook_handler":"true_1167848#1","key_yyb_image_clean_compress_enabled":"true_1625140#2","key_enable_ionia_starter_plugin":"true_495381#1","BookingPreDown.Feature.enableScreenOff":"true_1772919#1","key_widget_add_new_privilege":"true_671242#3","key_qd_downloader_disable_dual_down":"false_1687309#2","enable_report_oaid_and_android_id":"false_1556913#2","key_desktop_window_use_launcher_visible_event":"2016_535399#1","down_load_page_app_cloud_game_style":"true_1567130#4","key_cloud_disk_preview_black_list":"mkv_2276540#1","key_middle_page_welfare_game_download_layer_display":"true_2172227#1","key_enable_log_upload_in_FAQ":"true_439954#1","key_exit_guid_of_game_page":"true_440456#1","key_discover_slide_video_show_cover":"true_2183032#1","key_enable_daemon_start_res_monitor":"true_2305227#1","key_fix_app_detail_auto_download_fail_switch":"true_2006175#1","enable_optimize_search_init_page":"true_846241#4","show_install_tips":"false_309175#2","key_accessibility_enable":"false_756097#1","constellation_widget_solution_type":"4_873864#1","key_ai_zone_center_page_floatball":"1596203#2","key_enable_ionia_starter_plugin_v3":"true_1897613#1","key_phantom_plugin_app_config":"2340006#1","ai_zone_no_need_login":"true_1733685#2","key_wx_miniprogram_max_shortcut_prompts_per_day":"2_774872#1","key_forbbid_request_private_storage_permission":"true_1937652#1","BookingDownloaderFeature.switch.channelPkgEnableDual":"false_1762275#1","key_enable_merge_apk_report":"true_1728878#1","constellation_widget_margin":"60_876346#2","key_photon_card_preload":"true_1775558#1","key_update_booking_download":"true_1788492#3","key_install_fail_extra_report":"true_1949456#1","top_header_view_alpha_transparent_switch":"false_1956425#1","key_direct_jump_main_enable":"false_633206#1","key_app_details_official_desc_tencent_download":"2_2093567#1","DynamicSplash.switch.enableSplashPriority":"false_1548535#2","key_peak_cut_delay_time":"3000_2313676#1","key_download_notification_flag":"2_1125723#1","key_qd_downloader_expand_transports_num_54153350":"5_2219333#1","key_interrupt_float_layer_show":"1671990#2","KEY_BACKUP_WECHAT_MANUAL":"true_2084667#1","config_bpd_calendar_enable":"true_1965718#1","kuikly_key_region_config_v2":"1725689#1","key_replace_channel_support_reflect":"true_1656736#4","key_ionia_brand_display_black_List":"none_501249#3","key_get_device_name_switch":"true_319431#2","key_desktop_window_2016_skip_use_launcher_visible":"true_2096626#1","key_other_app_clean_desk_msg_data_permission":"true_1956629#1","enable_avid_un_shell_update_auto_download":"true_1505139#2","key_enable_game_tab_preload_anchor":"true_1725671#1","key_cmd_dual_enable":"_2328528#2","BookingFeature.switch.enableBookingButtonWelfareState":"false_1671485#1","key_cloud_disk_app_backup":"true_2316882#1","key_ka_res_load_switch":"false_1870881#1","key_ionia_strategy_StartByAllowListTokenStrategy":"true_2296501#1","BookingDownloaderFeature.config.bookingPreSvrFlowStateUpdateIntervalSecs":"30_1736367#1","enable_report_shell_download_time":"true_1173521#2","key_rdelivery_disable_anti_freezing":"false_1516310#2","key_kuikly_pc_game_new_game_bottom_direct_config":"_2262304#1","key_wxqq_clean_cloud_disk_entrance_switch":"true_2135599#1","config_bpd_calendar_app_data_54418333":"2140858#2","BookingDownloaderFeature.config.defaultClientPullDataIntevalTime":"3600_1737063#1","key_enable_minigame_info_report":"true_882685#1","key_has_desktop_window_behavior_permission":"true_335141#1","key_normal_splash_add_video_kind":"true_2310549#1","enable_create_constellation_widget":"true_914064#2","key_ka_invoke_max_times":"-1_1870895#1","key_ionia_enable_abort_detect":"false_1890742#1","mechanized_popup_request_frequency_switch":"true_2263474#1","key_new_photo_clean_scan":"true_1160413#7","photon_home_page_engine_add_photon_version_switch":"true_2089715#1","is_hw_aware_open_gray":"false_1657494#1","enable_delay_shell_download":"false_1877371#1","BookingDownloaderFeature.switch.disableOldBookingRequest":"true_1788374#1","enable_dump_king_card_state":"true_1057369#1","key_new_plugin_loading_page":"false_758387#1","key_enable_install_with_allow_permission_for_allow_brand_rdelivery":"false_827377#1","key_pag_switch":"true_415054#1","key_discover_recommend_guide_config":"0,2,-1,-1,-1_1917231#2","key_desktop_window_open_float_window":"true_335142#1","enable_report_king_card_state":"false_1036209#1","key_patch_support_format":"800_2272241#1","key_enable_ionia_starter_plugin_v2":"true_1890828#1","key_ionia_brand_display_black_List_v2":"google,tcl,blackberry_1881716#3","key_start_trail_force_download_info":"_780248#1","key_middle_page_welfare_new_user_card":"true_2179112#1","key_enable_custom_service":"true_739737#1","is_hw_aware_open_872":"false_1746603#1","AutoDownloadFeature.switch.enableAutoDownloadOnMobileNetwork":"true_1748458#1","BookingDownloaderFeature.config.reportTaskIntevalTime":"1_1722175#1","key_dual_new_guide":"true_1872336#1","key_welfare_login_style":"true_1840667#3","key_request_full_rate_duration":"108000_1562934#1","key_discover_default_tab_position":"2_2235089#1","key_sp_never_process_work_on_main_thread":"true_613809#1","key_enable_phantom_all_app_on_white_list":"false_1560885#1","key_ionia_compat_phantom_version":"160_1620833#1","key_cg_quick_start_adjust_enable":"true_1607748#5","key_vivo_use_adb_install":"false_765677#1","BookingDownloaderFeature.switch.enableAppBackground":"true_1772933#1","plugin_oom_release_context":"false_1609772#1","key_float_on_op":"false_488328#2","enable_pop_window_when_splash_hidden":"true_1556467#1","key_score_config":"2118928#1","key_enable_kr_report_element_exposure":"true_2189576#1","kuikly_key_region_config":"1720768#1","key_app_clean_pkg_names":"2298727#1","test_config":"0_329100#1","key_enable_hook_base_context_res":"true_2313633#1","key_book_calendar_config":"2140173#1","BookingDownloaderFeature.switch.enableBattery":"true_1772928#1","key_init_cloud_disk_transfer_task_when_yyb_launch":"true_2043817#2","BookingDownloaderFeature.switch.enableRunningTask":"true_1772938#1","BookingPreDown.Feature.enableAppBackground":"true_1772918#1","key_aigc_wallpaper_prebuilt_delay":"3000_2047615#2","traffic_default_max_download_size":"200_1781762#2","key_enable_kr_app_stub_text_size":"true_2189573#1","key_patch_enable_strategy":"true_2272243#1","qd_downloader_save_buff_full_size":"100_2203976#1","key_pure_enhance_mode_check":"true_2181550#1","key_enable_report_app":"_1265305#1","key_plugin_update_threshold":"300000_493574#1","key_qd_downloader_enable_expand_transports":"true_1753544#2","key_middle_page_dialog_game_download_layer_display":"true_2181096#1","key_enable_phantom_all_staging_page":"true_1548514#2","key_enable_qd_downloader_log":"true_1509568#2","key_rdelivery_clean_result_style":"1_1655382#1","key_new_traffic_dialog_exp1":"true_1951125#1","key_discover_video_recycle_force_stop":"false_2027508#1","config_bpd_calendar_app_data_54229156":"2066983#1","key_enable_android_data_bypass":"true_1980041#1","config_bpd_calendar_app_list":"54418333_2043461#2","key_pkg_change_task_fix_enabled":"false_1622698#1","BookingDownloaderFeature.switch.enableScreenOff":"true_1772934#1","key_enable_middle_view_model_fix_9":"false_1643036#1","key_enable_download_speed_zero":"true_822438#1","key_enable_ionia_dismiss_delay":"false_1745413#1","BookingPreDown.Feature.enableBattery":"true_1772911#1","key_qd_downloader_enable_trans_control_v2":"true_1753538#2","enable_king_card_toast_style":"true_1951594#1","key_enable_freeze_frame_report":"true_591415#4","enable_max_download_number":"1_2193641#2","key_disable_start_patch_on_launch":"true_2272231#1","key_bk_on_ruok":"true_482812#9","key_browser_h5_auto_download":"false_1303626#3","key_process_alive_looper_duration":"3600000_537925#3","key_kuikly_preload_page_config":"2250068#1","key_enable_mechanize_popup_home":"true_2263475#1","key_tab_double_image_config_enable":"true_1728572#1","check_download_flow":"true_1165724#1","key_install_session_jump_config":"_1522876#2","key_enable_new_no_wifi_download_dialog":"true_1781751#2","key_ionia_strategy_StartByAccountServiceStrategy":"true_2268923#4","key_ionia_notify_media_session":"true_2295177#2","kuikly_ABTest_config":"2277300#3","key_low_latency_config":"31_519366#2","file_format_open_map":"1738536#1","key_dual_user_do_not_show_traffic_dialog":"true_1704360#3","BookingDownloaderFeature.switch.channelPkgEnable5GDirect":"false_1762276#1","key_get_bluetooth_name_switch":"true_319453#2","key_game_id_enable_sr":"755369#3","key_video_clean_cloud_disk_entrance_switch":"true_2135981#1","key_rdelivery_fix_honor_huawei_push":"true_1711907#1","key_is_first_page_cache_enable":"false_522344#3","key_peak_cut_hour_config":"0;7;8;15_2313675#1","key_ka_res_request_interval":"604800000_1876242#1","key_qd_downloader_enable_switch_url":"true_1597895#1","enable_check_version_before_load_video_plugin":"true_1528078#1","config_cmd_enable_net_probe":"1_2266956#1","key_qd_downloader_enable_check_cost_time_fix":"1_2275327#2","BookingPreDown.Feature.enableStorage":"true_1772916#1","key_desktop_black_list_using_v2_key":"true_1897598#1","key_sp_never_waiting_finish_queue":"true_613803#1","key_allow_cp_query_yyb_info_apps":"1808386#2","key_big_file_clean_cloud_disk_entrance_switch":"true_2135958#1","key_install_retry_enable_retry_task":"true_1897552#1","key_qd_downloader_expand_transports_num":"2_2202908#1","welfare_should_award_point":"false_1630205#1","key_hw_aware_model_config":"1703593#1","key_qd_downloader_enable_queue_v2":"true_1699835#1","enable_optimize_search_result_page":"true_846260#7","DynamicSplash.switch.enableDynamicSplash":"true_1548531#7","key_bk_max_ver":"13.0.36.0_493943#1","key_desktop_beacon_report_rate_rule":"true_490679#1","key_downloading_float_show_min_size_mb":"300_778181#1","enable_download_version_code_suffix_white_list":"2080007#4","key_other_app_clean_config":"1704269#1","key_enable_topview_show_bubble_when_downloaded":"true_1708318#2","key_enable_sr":"true_755386#2","key_installer_config":"1894619#1","key_enable_necessary_window_v2":"true_1789611#1","key_feedback_tel_phone":"4006%20700%20700_1435735#1","key_outer_download_succ_launch_config":"{}_2317944#1","key_ea_dex_class_loader_switch":"true_2062739#1","key_0927_enable_codec_detecti
[I][2025-04-02 +80 20:59:03.378][11932, 1*][main][market][BaseActivity][][onUserLeaveHint
[I][2025-04-02 +80 20:59:03.379][11932, 1*][main][market][FLog_onPause][][[main]SettingActivity,
[I][2025-04-02 +80 20:59:03.379][11932, 1*][main][market][ProcessLifecycleManager][][onActivityPaused, activity: com.tencent.nucleus.manager.setting.SettingActivity@accda01
[I][2025-04-02 +80 20:59:03.382][11932, 1*][main][market][BaseActivity][][[ActivityLifeCycle] onPause com.tencent.nucleus.manager.setting.SettingActivity
[I][2025-04-02 +80 20:59:03.409][11932, 1*][main][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:03.409][11932, 1*][main][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:03.422][11932, 1*][main][market][FLog_onStop][][[main]MainActivity,
[I][2025-04-02 +80 20:59:03.423][11932, 1*][main][market][ProcessLifecycleManager][][onActivityStopped, activity: com.tencent.assistantv2.activity.MainActivity@94fefe1
[I][2025-04-02 +80 20:59:03.423][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ onActivityStopped activity name :MainActivity
[I][2025-04-02 +80 20:59:03.423][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ updateActivityVisibleCount mActivityVisibleCount=1
[I][2025-04-02 +80 20:59:03.424][11932, 76][proc_lifecycle_proxy][market][yyb8922819.j2.xd][][ getItem  from active key = 1, fragment = xb{5a0a790} (ef751226-229a-4cc4-b0c7-05f188122f25 id=0x7f080a3f tag=android:switcher:2131233343:1)
[I][2025-04-02 +80 20:59:03.429][11932, 1*][main][market][BaseActivity][][[ActivityLifeCycle] onStop com.tencent.assistantv2.activity.MainActivity
[I][2025-04-02 +80 20:59:03.430][11932, 1*][main][market][VideoViewComponent_DiscoverRecommend_10843_139699436][][onStop() called with: context = [com.tencent.assistantv2.activity.MainActivity@94fefe1], this: com.tencent.assistant.component.video.view.VideoViewComponentV2{853a4ec VFE...C.. ......I. 0,0-0,0}
[I][2025-04-02 +80 20:59:03.430][11932, 1*][main][market][playerViewVisibleChanged_0][][【onStop】visiblei: false playerViewVisible: false isHuyaVideo: false url: null
[I][2025-04-02 +80 20:59:03.430][11932, 1*][main][market][playerViewVisibleChanged_0][][【backHome】visiblei: false playerViewVisible: false isHuyaVideo: false url: null
[I][2025-04-02 +80 20:59:03.430][11932, 1*][main][market][VideoViewComponent_DiscoverRecommend_10843_139699436][][onStop, playerViewStateChangeListener: null needHandlerBySelf: true
[I][2025-04-02 +80 20:59:03.431][11932, 1*][main][market][assistant][][tryStopPlay return,  isStopped: false, isInitPlayStatus: true
[I][2025-04-02 +80 20:59:03.431][11932, 1*][main][market][VideoViewComponent_DiscoverRecommend_10843_73786143][][onStop() called with: context = [com.tencent.assistantv2.activity.MainActivity@94fefe1], this: com.tencent.assistant.component.video.view.VideoViewComponentV2{465e31f VFE...C.. ........ 539,1169-540,1170}
[I][2025-04-02 +80 20:59:03.431][11932, 1*][main][market][VideoViewComponent_DiscoverRecommend_10843_73786143][][onStop, playerViewStateChangeListener: null needHandlerBySelf: true
[I][2025-04-02 +80 20:59:03.431][11932, 1*][main][market][assistant][][tryStopPlay return,  isStopped: true, isInitPlayStatus: false
[I][2025-04-02 +80 20:59:03.431][11932, 1*][main][market][tag_LaunchSpeed][][[main],[LaunchTagger][1]clear tags in market process.
[I][2025-04-02 +80 20:59:03.440][11932, 1*][main][market][FLog_onStop][][[main]SettingActivity,
[I][2025-04-02 +80 20:59:03.441][11932, 1*][main][market][ProcessLifecycleManager][][onActivityStopped, activity: com.tencent.nucleus.manager.setting.SettingActivity@accda01
[I][2025-04-02 +80 20:59:03.441][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ onActivityStopped activity name :SettingActivity
[I][2025-04-02 +80 20:59:03.441][11932, 1*][main][market][ActivityLifecycleListenerImpl][][ updateActivityVisibleCount mActivityVisibleCount=0
[I][2025-04-02 +80 20:59:03.447][11932, 1*][main][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:03.447][11932, 210][vrpool-1-thread][market][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:03.447][11932, 210][vrpool-1-thread][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:59:03.447][11932, 1*][main][market][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:59:03.453][11932, 1*][main][market][BaseActivity][][[ActivityLifeCycle] onStop com.tencent.nucleus.manager.setting.SettingActivity
[I][2025-04-02 +80 20:59:03.522][11932, 1*][main][market][RedDot][][updateRedDotRunnable
[I][2025-04-02 +80 20:59:03.522][11932, 1*][main][market][redDot][][setRedDotWithType: tabType = 3 bundle = Bundle[{bubbleNumber=0, tabType=3, showRedDot=false, currentTabType=3}]
[I][2025-04-02 +80 20:59:03.586][11932, 54][temporary-7][market][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:03.586][11932, 54][temporary-7][market][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:04.004][11932, 1*][main][market][GlobalMonitor][][app go background, process:market
[I][2025-04-02 +80 20:59:04.004][11932, 1*][main][market][download-floating-window][][receive UI_EVENT_APP_GOBACKGROUND
[I][2025-04-02 +80 20:59:04.004][11932, 1*][main][market][PassPhraseManager][][App go background
[I][2025-04-02 +80 20:59:04.006][11932, 1*][main][market][StatusBarEventController][][#handleUIEvent: msg.what=1039
[I][2025-04-02 +80 20:59:04.008][11932, 1*][main][market][StatusBarEventController][][#handleOther: what=1039
[I][2025-04-02 +80 20:59:04.008][11932, 1*][main][market][StorageOptimizeService][][app go background
[E][2025-04-02 +80 20:59:04.507][11932, 190][TVK-Scheduled-2][market][TVKPlayer[TVKNetworkUtils]][][java.lang.Exception: fail to getNetworkInterfaces
	at com.tencent.qqlive.tvkplayer.tools.utils.s.f(ProGuard:25)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.e(ProGuard:14)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.h(ProGuard:3)
	at com.tencent.qqlive.tvkplayer.tools.utils.s.a(ProGuard:4)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.f(ProGuard:1)
	at com.tencent.qqlive.tvkplayer.tools.utils.u.vida(Unknown Source:0)
	at plugin_video.vidp.vidd.run(Unknown Source:0)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:307)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:302)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
【plugin_version：111 】
[I][2025-04-02 +80 20:59:04.967][11932, 342][IntentService[PreWiseMessageQueue]][market][ShellUpdateManager][][getWisePreDownloadResponse allowUnShellUpdateAutoDownload：false, enableAvidAutoDownload：true, isUseShellUpdate：false
[I][2025-04-02 +80 20:59:04.967][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][defaultResponse: 
[I][2025-04-02 +80 20:59:04.982][11932, 39][temporary-2][market][ReplaceMonitorManager][][cmd = 5, paramsJson = , extParamsJson = 
[I][2025-04-02 +80 20:59:04.983][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][startTime:1743523200982 endTime:1743609600982
[I][2025-04-02 +80 20:59:04.983][11932, 342][IntentService[PreWiseMessageQueue]][market][assistant][][mSwitchCondiction = true, mTimeCondiction = true, mPrimaryCondition = true, mOtherCondiction = true
[I][2025-04-02 +80 20:59:04.983][11932, 39][temporary-2][market][ReplaceMonitorMsgProxy][][invokePlugin 完成
[I][2025-04-02 +80 20:59:04.983][11932, 342][IntentService[PreWiseMessageQueue]][market][gray_update][][WisePreDownloadMonitor tryDownload
[I][2025-04-02 +80 20:59:04.983][11932, 39][temporary-2][market][ReplaceMonitorMsgProxy][][queryReplaceRecord from mainThread: json = []
[I][2025-04-02 +80 20:59:04.984][11932, 1*][main][market][PreUpdateAppEngine][][删除换包： size = 0
[I][2025-04-02 +80 20:59:04.985][11932, 342][IntentService[PreWiseMessageQueue]][market][PreUpdateAppEngine][][isPreUpdateDownloadSwitch isSwitchOpen=true
[I][2025-04-02 +80 20:59:04.985][11932, 342][IntentService[PreWiseMessageQueue]][market][MonitorHandler][][MonitorHandler call tryAutoDownload
[I][2025-04-02 +80 20:59:05.329][11932, 1*][main][market][DarkModeObserver][][DarkModeObserver =false
