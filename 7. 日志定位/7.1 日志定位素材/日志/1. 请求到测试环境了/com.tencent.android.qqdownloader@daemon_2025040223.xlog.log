[W][2025-04-02 +80 20:24:44.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:44.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:24:46.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:46.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:24:47.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:47.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:24:47.822][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 300134
[I][2025-04-02 +80 20:24:47.822][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:24:47.825][11862, 43][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8133秒]
[I][2025-04-02 +80 20:24:47.825][11862, 39][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:24:47.826][11862, 43][temporary-5][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:24:48.993][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 2 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10002 , taskStartElapsedRealtimeMs = 448318036 , reportEventTime = 448328038
[W][2025-04-02 +80 20:24:49.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:49.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:24:50.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:50.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:24:52.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:52.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:24:53.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:53.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:24:55.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:55.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:24:56.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:56.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:24:58.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:58.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:24:59.001][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 7 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10007 , taskStartElapsedRealtimeMs = 448328039 , reportEventTime = 448338046
[W][2025-04-02 +80 20:24:59.651][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:24:59.654][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:25:00.021][11862, 37][temporary-2][daemon][FLog_login_log][][[temporary-2]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 20:25:01.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:01.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:02.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:02.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:04.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:04.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:05.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:05.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:07.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:07.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:08.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:08.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:25:09.011][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 448338046 , reportEventTime = 448348056
[W][2025-04-02 +80 20:25:10.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:10.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:11.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 20:25:11.694][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 20:25:11.694][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 20:25:11.695][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 20:25:11.696][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 20:25:11.696][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 20:25:11.697][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 20:25:13.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:13.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:14.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:14.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:16.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:16.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:17.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:17.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:25:17.837][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 330149
[I][2025-04-02 +80 20:25:17.837][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:25:17.840][11862, 44][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8163秒]
[I][2025-04-02 +80 20:25:17.841][11862, 44][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:25:17.841][11862, 42][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:25:19.013][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 448348057 , reportEventTime = 448358058
[W][2025-04-02 +80 20:25:19.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:19.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:20.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:20.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:22.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:22.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:23.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:23.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:25.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:25.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:26.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:26.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:28.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:28.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:25:29.022][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 448358059 , reportEventTime = 448368067
[W][2025-04-02 +80 20:25:29.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:29.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:31.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:31.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:32.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:32.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:34.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:34.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:35.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:35.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:37.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:37.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:38.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:38.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:25:39.030][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 448368067 , reportEventTime = 448378075
[W][2025-04-02 +80 20:25:40.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:40.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:41.653][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:41.657][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:43.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:43.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:44.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:44.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:46.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:46.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:47.651][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:47.654][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:25:47.850][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 360162
[I][2025-04-02 +80 20:25:47.850][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:25:47.853][11862, 39][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:25:47.855][11862, 35][temporary-1][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8193秒]
[I][2025-04-02 +80 20:25:47.857][11862, 35][temporary-1][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:25:49.031][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 448378075 , reportEventTime = 448388076
[W][2025-04-02 +80 20:25:49.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:49.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:50.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:50.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:52.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:52.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:53.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:53.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:55.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:55.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:56.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:56.657][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:25:58.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:58.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:25:59.042][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 448388077 , reportEventTime = 448398087
[W][2025-04-02 +80 20:25:59.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:25:59.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:26:00.012][11862, 35][temporary-1][daemon][FLog_login_log][][[temporary-1]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 20:26:01.153][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:01.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:02.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:02.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:04.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:04.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:05.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:05.657][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:07.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:07.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:08.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:08.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:26:09.050][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 448398087 , reportEventTime = 448408095
[W][2025-04-02 +80 20:26:10.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:10.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:11.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:11.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:13.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 20:26:13.161][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 20:26:13.162][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 20:26:13.162][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 20:26:13.163][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 20:26:13.164][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 20:26:13.165][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 20:26:14.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:14.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:16.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:16.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:17.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:17.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:26:17.861][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 390173
[I][2025-04-02 +80 20:26:17.863][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:26:17.865][11862, 37][temporary-2][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8223秒]
[I][2025-04-02 +80 20:26:17.866][11862, 46][temporary-8][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:26:17.867][11862, 37][temporary-2][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:26:19.051][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 448408095 , reportEventTime = 448418096
[W][2025-04-02 +80 20:26:19.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:19.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:20.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:20.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:22.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:22.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:23.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:23.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:25.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:25.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:26.650][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:26.653][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:28.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:28.153][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:26:29.061][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 448418097 , reportEventTime = 448428106
[W][2025-04-02 +80 20:26:29.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:29.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:31.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:31.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:32.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:32.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:34.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:34.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:35.651][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:35.652][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:37.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:37.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:38.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:38.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:26:39.070][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 448428106 , reportEventTime = 448438115
[W][2025-04-02 +80 20:26:40.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:40.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:41.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:41.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:43.153][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:43.157][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:44.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:44.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:46.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:46.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:47.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:47.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:26:47.874][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 420186
[I][2025-04-02 +80 20:26:47.874][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:26:47.876][11862, 45][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8253秒]
[I][2025-04-02 +80 20:26:47.877][11862, 39][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:26:47.878][11862, 45][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:26:49.070][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 0 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10000 , taskStartElapsedRealtimeMs = 448438115 , reportEventTime = 448448115
[W][2025-04-02 +80 20:26:49.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:49.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:50.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:50.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:52.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:52.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:53.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:53.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:55.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:55.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:56.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:56.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:26:58.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:58.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:26:59.077][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 7 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10007 , taskStartElapsedRealtimeMs = 448448115 , reportEventTime = 448458122
[W][2025-04-02 +80 20:26:59.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:26:59.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:27:00.012][11862, 46][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 20:27:01.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:01.153][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:02.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:02.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:04.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:04.153][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:05.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:05.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:07.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:07.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:08.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:08.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:27:09.088][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 448458123 , reportEventTime = 448468133
[W][2025-04-02 +80 20:27:10.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:10.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:11.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:11.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:13.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:13.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:14.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 20:27:14.660][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 20:27:14.661][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 20:27:14.662][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 20:27:14.663][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 20:27:14.664][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 20:27:14.665][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 20:27:16.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:16.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:17.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:17.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:27:17.886][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 450197
[I][2025-04-02 +80 20:27:17.886][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:27:17.888][11862, 42][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:27:17.888][11862, 37][temporary-2][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8283秒]
[I][2025-04-02 +80 20:27:17.890][11862, 37][temporary-2][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:27:19.089][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 448468133 , reportEventTime = 448478134
[W][2025-04-02 +80 20:27:19.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:19.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:20.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:20.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:22.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:22.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:23.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:23.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:25.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:25.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:26.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:26.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:28.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:28.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:27:29.094][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 4 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10004 , taskStartElapsedRealtimeMs = 448478135 , reportEventTime = 448488139
[W][2025-04-02 +80 20:27:29.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:29.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:31.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:31.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:32.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:32.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:34.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:34.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:27:34.887][11862, 1*][main][daemon][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[W][2025-04-02 +80 20:27:35.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:35.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:37.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:37.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:38.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:38.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:27:39.101][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 6 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10006 , taskStartElapsedRealtimeMs = 448488140 , reportEventTime = 448498146
[W][2025-04-02 +80 20:27:40.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:40.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:41.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:41.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:43.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:43.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:44.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:44.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:46.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:46.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:47.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:47.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:27:47.898][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 480210
[I][2025-04-02 +80 20:27:47.899][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:27:47.901][11862, 45][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8313秒]
[I][2025-04-02 +80 20:27:47.901][11862, 39][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:27:47.903][11862, 45][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:27:49.103][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 448498147 , reportEventTime = 448508148
[W][2025-04-02 +80 20:27:49.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:49.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:50.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:50.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:52.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:52.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:53.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:53.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:55.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:55.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:56.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:56.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:27:58.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:58.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:27:59.114][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 448508148 , reportEventTime = 448518159
[W][2025-04-02 +80 20:27:59.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:27:59.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:28:00.016][11862, 39][temporary-3][daemon][FLog_login_log][][[temporary-3]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 20:28:01.150][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:01.151][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:02.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:02.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:04.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:04.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:05.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:05.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:07.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:07.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:08.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:08.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:28:09.118][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 4 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10004 , taskStartElapsedRealtimeMs = 448518159 , reportEventTime = 448528163
[W][2025-04-02 +80 20:28:10.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:10.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:11.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:11.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:13.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:13.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:14.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:14.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:16.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 20:28:16.196][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 20:28:16.197][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 20:28:16.198][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 20:28:16.199][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 20:28:16.200][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 20:28:16.201][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[W][2025-04-02 +80 20:28:17.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:17.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:28:17.909][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 510221
[I][2025-04-02 +80 20:28:17.909][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:28:17.913][11862, 39][temporary-3][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8343秒]
[I][2025-04-02 +80 20:28:17.914][11862, 39][temporary-3][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:28:17.914][11862, 46][temporary-8][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:28:19.120][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 2 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10002 , taskStartElapsedRealtimeMs = 448528163 , reportEventTime = 448538165
[W][2025-04-02 +80 20:28:19.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:19.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:20.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:20.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:22.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:22.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:23.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:23.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:25.153][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:25.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:26.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:26.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:28.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:28.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:28:29.127][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 6 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10006 , taskStartElapsedRealtimeMs = 448538166 , reportEventTime = 448548172
[W][2025-04-02 +80 20:28:29.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:29.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:31.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:31.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:32.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:32.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:34.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:34.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:35.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:35.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:37.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:37.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:38.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:38.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:28:39.138][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 10 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10010 , taskStartElapsedRealtimeMs = 448548173 , reportEventTime = 448558183
[W][2025-04-02 +80 20:28:40.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:40.153][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:41.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:41.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:43.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:43.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:44.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:44.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:46.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:46.153][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:47.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:47.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:28:47.919][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 540231
[I][2025-04-02 +80 20:28:47.920][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:28:47.923][11862, 35][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:28:47.923][11862, 46][temporary-8][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8373秒]
[I][2025-04-02 +80 20:28:47.924][11862, 46][temporary-8][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:28:49.140][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 2 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10002 , taskStartElapsedRealtimeMs = 448558183 , reportEventTime = 448568185
[W][2025-04-02 +80 20:28:49.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:49.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:50.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:50.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:52.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:52.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:28:53.137][11862, 58][common_task_pool-58][daemon][halley-cloud-StateHandler][][onDisconnected
[I][2025-04-02 +80 20:28:53.138][11862, 1*][main][daemon][BookingPreDown.LongConnEngine][][onDisconnected
[I][2025-04-02 +80 20:28:53.138][11862, 1*][main][daemon][UpdateBookingLongConnEngine][][onDisconnected
[W][2025-04-02 +80 20:28:53.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:53.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:55.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:55.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:56.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:56.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:28:58.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:58.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:28:59.146][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 6 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10006 , taskStartElapsedRealtimeMs = 448568185 , reportEventTime = 448578191
[W][2025-04-02 +80 20:28:59.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:28:59.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:29:00.010][11862, 39][temporary-3][daemon][FLog_login_log][][[temporary-3]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 20:29:01.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:01.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:02.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:02.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:04.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:04.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:05.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:05.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:07.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:07.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:08.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:08.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:29:09.154][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 8 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10008 , taskStartElapsedRealtimeMs = 448578191 , reportEventTime = 448588199
[W][2025-04-02 +80 20:29:10.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:10.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:11.651][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:11.653][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:13.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:13.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:14.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:14.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:16.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:16.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:17.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 20:29:17.661][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 20:29:17.662][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 20:29:17.663][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 20:29:17.664][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 20:29:17.664][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 20:29:17.665][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 20:29:17.930][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 570242
[I][2025-04-02 +80 20:29:17.930][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:29:17.933][11862, 42][temporary-4][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8403秒]
[I][2025-04-02 +80 20:29:17.934][11862, 42][temporary-4][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:29:17.934][11862, 39][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:29:18.768][11862, 65][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:29:18.770][11862, 72][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:29:18.786][11862, 68][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[W][2025-04-02 +80 20:29:19.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 20:29:19.155][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 448588199 , reportEventTime = 448598200
[E][2025-04-02 +80 20:29:19.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:20.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:20.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:22.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:22.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:23.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:23.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:25.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:25.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:26.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:26.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:28.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:28.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:29:29.166][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 448598200 , reportEventTime = 448608211
[W][2025-04-02 +80 20:29:29.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:29.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:31.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:31.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:32.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:32.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:34.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:34.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:35.651][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:35.652][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:37.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:37.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:38.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:38.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:29:39.175][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 448608211 , reportEventTime = 448618220
[W][2025-04-02 +80 20:29:40.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:40.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:41.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:41.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:43.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:43.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:44.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:44.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:46.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:46.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:47.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:47.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:29:47.941][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250402 , nowCount = 0 , lastExecuteTime = 1743596387688
[I][2025-04-02 +80 20:29:47.942][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-02 +80 20:29:47.944][11862, 43][temporary-5][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[I][2025-04-02 +80 20:29:47.947][11862, 42][temporary-4][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8433秒]
[I][2025-04-02 +80 20:29:47.948][11862, 39][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:29:47.948][11862, 42][temporary-4][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:29:47.950][11862, 43][temporary-5][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[W][2025-04-02 +80 20:29:49.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:49.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:29:49.177][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 448618221 , reportEventTime = 448628222
[W][2025-04-02 +80 20:29:50.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:50.654][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:52.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:52.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:53.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:53.657][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:55.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:55.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:56.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:56.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:29:58.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:58.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:29:59.187][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 448628223 , reportEventTime = 448638232
[W][2025-04-02 +80 20:29:59.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:29:59.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:00.020][11862, 44][temporary-6][daemon][FLog_login_log][][[temporary-6]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 20:30:01.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:01.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:02.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:02.653][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:04.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:04.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:05.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:05.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:07.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:07.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:08.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:08.657][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:09.197][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 9 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10009 , taskStartElapsedRealtimeMs = 448638233 , reportEventTime = 448648242
[W][2025-04-02 +80 20:30:10.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:10.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:11.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:11.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:13.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:13.155][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:14.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:14.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:16.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:16.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:17.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:17.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:17.958][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 30016
[I][2025-04-02 +80 20:30:17.958][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:30:17.960][11862, 45][temporary-7][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8463秒]
[I][2025-04-02 +80 20:30:17.961][11862, 45][temporary-7][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:30:17.962][11862, 42][temporary-4][daemon][TimerCleanManager][][storage permission not granted
[W][2025-04-02 +80 20:30:19.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 20:30:19.194][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 20:30:19.195][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 20:30:19.196][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 20:30:19.197][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 20:30:19.198][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 20:30:19.198][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 20:30:19.199][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 1 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10001 , taskStartElapsedRealtimeMs = 448648243 , reportEventTime = 448658244
[W][2025-04-02 +80 20:30:20.651][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:20.654][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:22.152][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:22.156][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:23.652][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:23.654][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:24.285][11862, 360][Binder:11862_D][daemon][Tinker.ProcessLifecycle][][App是进后台检查: activityCount=1,hasVisibleActivity=true
[I][2025-04-02 +80 20:30:24.310][11862, 360][Binder:11862_D][daemon][Tinker.ProcessLifecycle][][App是进后台检查: activityCount=0,hasVisibleActivity=false
[I][2025-04-02 +80 20:30:24.310][11862, 360][Binder:11862_D][daemon][Tinker.ProcessLifecycle][][【应用宝】进入后台:{"app_time":405514, "last_scene":2035}
[I][2025-04-02 +80 20:30:24.368][11862, 360][Binder:11862_D][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:30:24.735][11862, 1*][main][daemon][UserStateReporter][][onAppStateChangeToMainThread: isFront = false, currentPageId = 2035, isAppFront = true
[I][2025-04-02 +80 20:30:24.735][11862, 1*][main][daemon][UserStateReporter][][onAppStateChange: isFront = false , currentPageId = 2035 , isAppFront = true
[I][2025-04-02 +80 20:30:24.735][11862, 1*][main][daemon][UserStateReporter][][onAppStateChange: app to background>>>> stayDurationMs = 405926 , currentPageId = 2035
[I][2025-04-02 +80 20:30:24.742][11862, 1*][main][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:30:24.742][11862, 1*][main][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:30:24.743][11862, 1*][main][daemon][UserStateReporter][][reportUserStayDuration: success, durationMs = 405926 , scene = 2035
[I][2025-04-02 +80 20:30:24.743][11862, 1*][main][daemon][UserStateReporter][][stopHeartbeatReport: called.
[I][2025-04-02 +80 20:30:24.743][11862, 1*][main][daemon][UserStateReporter][][onAppStateChange: isAppFront = false , currentPageId = 2035
[I][2025-04-02 +80 20:30:24.744][11862, 1*][main][daemon][GlobalMonitor][][app go background, process:daemon
[I][2025-04-02 +80 20:30:24.748][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_OFF
[I][2025-04-02 +80 20:30:24.763][11862, 43][temporary-5][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:30:24.763][11862, 43][temporary-5][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:30:24.764][11862, 43][temporary-5][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:30:25.012][11862, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=100
[W][2025-04-02 +80 20:30:25.151][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:25.152][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:25.478][11862, 46][temporary-8][daemon][FLog_login_log][][[temporary-8]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:30:25.741][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_ON
[W][2025-04-02 +80 20:30:26.261][11862, 118][LogProcessService-1][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:30:26.262][11862, 118][LogProcessService-1][daemon][HttpNetWorkTaskV2][][[283]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:283,useHttp2:false
[I][2025-04-02 +80 20:30:26.290][11862, 420][protocalManagerStatReport-420][daemon][HttpNetWorkTaskV2][][[283]callStart
[I][2025-04-02 +80 20:30:26.291][11862, 420][protocalManagerStatReport-420][daemon][HttpNetWorkTaskV2][][[283]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-02 +80 20:30:26.313][11862, 420][protocalManagerStatReport-420][daemon][CustomDns-mayybstatnew.3g.qq.com][][lookupImpl ret:[/*************, /**************]
[I][2025-04-02 +80 20:30:26.314][11862, 420][protocalManagerStatReport-420][daemon][HttpNetWorkTaskV2][][[283]connectStart
[I][2025-04-02 +80 20:30:26.329][11862, 420][protocalManagerStatReport-420][daemon][HttpNetWorkTaskV2][][[283]connectEnd protocol:http/1.1
[I][2025-04-02 +80 20:30:26.330][11862, 420][protocalManagerStatReport-420][daemon][HttpNetWorkTaskV2][][[283]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:30:26.360][11862, 420][protocalManagerStatReport-420][daemon][HttpNetWorkTaskV2][][[283]callEnd
[I][2025-04-02 +80 20:30:26.371][11862, 420][protocalManagerStatReport-420][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-02 +80 20:30:26.687][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:26.688][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:28.192][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:28.196][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:29.210][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 11 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10011 , taskStartElapsedRealtimeMs = 448658244 , reportEventTime = 448668255
[W][2025-04-02 +80 20:30:29.692][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:29.695][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:31.192][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:31.195][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:32.691][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:32.695][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:34.192][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:34.196][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[E][2025-04-02 +80 20:30:35.493][11862, 1*][main][daemon][FLog_MainBinderManager][][onServiceDisconnected,processFlag:daemon
[I][2025-04-02 +80 20:30:35.493][11862, 1*][main][daemon][IPCMonitor][][onServiceDisconnected daemon
[I][2025-04-02 +80 20:30:35.501][11862, 45][temporary-7][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 20:30:35.505][11862, 45][temporary-7][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ebdd55d u0a3080 SVC  bg:+11s322ms idle change:idle procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922819.tf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 20:30:35.509][11862, 45][temporary-7][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 20:30:35.514][11862, 45][temporary-7][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ebdd55d u0a3080 SVC  bg:+11s328ms idle change:idle procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922819.tf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[W][2025-04-02 +80 20:30:35.691][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:35.692][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:35.759][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_OFF
[W][2025-04-02 +80 20:30:37.168][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:37.172][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:44.076][11862, 37][temporary-2][daemon][FLog_login_log][][[temporary-2]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[W][2025-04-02 +80 20:30:44.390][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:44.392][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:44.462][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_ON
[I][2025-04-02 +80 20:30:44.951][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 5740 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 15740 , taskStartElapsedRealtimeMs = 448668256 , reportEventTime = 448683996
[W][2025-04-02 +80 20:30:45.891][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:45.893][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:47.362][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:47.366][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:48.854][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:48.858][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:50.392][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:50.396][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:51.892][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:51.895][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:53.374][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:53.378][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:53.704][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 65762
[I][2025-04-02 +80 20:30:53.704][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:30:53.707][11862, 42][temporary-4][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [8499秒]
[I][2025-04-02 +80 20:30:53.709][11862, 42][temporary-4][daemon][CmdMetrics][][all        1        100.0        99       未探测          {}                  
[I][2025-04-02 +80 20:30:53.710][11862, 42][temporary-4][daemon][CmdMetrics][][stat       1        100.0        99       未探测          {}                  
[I][2025-04-02 +80 20:30:53.711][11862, 43][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[W][2025-04-02 +80 20:30:54.892][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:54.895][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:54.992][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 41 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10041 , taskStartElapsedRealtimeMs = 448683996 , reportEventTime = 448694037
[W][2025-04-02 +80 20:30:56.391][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:56.398][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:30:56.952][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_OFF
[W][2025-04-02 +80 20:30:57.892][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:57.895][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:30:59.392][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:30:59.395][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[E][2025-04-02 +80 20:57:04.109][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: report freeze. diffMs = 1559117 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 1569117 , taskStartElapsedRealtimeMs = 448694037 , reportEventTime = 450263154
[I][2025-04-02 +80 20:57:04.109][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: duration = 1569117 , taskStartDate = 20250402 , currentDate = 20250402
[I][2025-04-02 +80 20:57:04.110][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: [20250402]-1569117 , durationMs = 1569117
[W][2025-04-02 +80 20:57:04.123][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 20:57:04.170][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:57:04.171][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:57:04.181][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:57:04.181][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:57:04.192][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250402 , nowCount = 0 , lastExecuteTime = 1743596987942
[I][2025-04-02 +80 20:57:04.192][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-02 +80 20:57:04.236][11862, 44][temporary-6][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[I][2025-04-02 +80 20:57:04.239][11862, 39][temporary-3][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:57:04.249][11862, 45][temporary-7][daemon][FLog_login_log][][[temporary-7]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:57:04.251][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 20:57:04.252][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 20:57:04.253][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 20:57:04.253][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 20:57:04.254][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 20:57:04.258][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 20:57:04.266][11862, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=100
[W][2025-04-02 +80 20:57:04.447][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:04.448][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:57:04.450][11862, 43][temporary-5][daemon][FLog_login_log][][[temporary-5]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:57:04.450][11862, 43][temporary-5][daemon][FLog_login_log][][[temporary-5]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:57:04.450][11862, 43][temporary-5][daemon][timer_job][][traceId:0 msg: on receive,action:com.tencent.android.qqdownloader.action.SCHEDULE_JOB_IN_DAEMON, jobClazzName:com.tencent.nucleus.manager.backgroundscan.BackgroundScanTimerJob
[I][2025-04-02 +80 20:57:04.451][11862, 304][Thread_TimerJobQueue][daemon][NewBackGroundScanManager][][[galtest] start scan
[W][2025-04-02 +80 20:57:04.451][11862, 304][Thread_TimerJobQueue][daemon][NewBackGroundScanManager][][未赋予存储权限
[I][2025-04-02 +80 20:57:04.452][11862, 44][temporary-6][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[I][2025-04-02 +80 20:57:04.454][11862, 44][temporary-6][daemon][timer_job][][traceId:0 msg: on receive,action:com.tencent.android.qqdownloader.action.SCHEDULE_JOB_IN_DAEMON, jobClazzName:com.tencent.pangu.module.timer.job.AutoDownloadTimerJob
[I][2025-04-02 +80 20:57:04.454][11862, 44][temporary-6][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [10070秒]
[I][2025-04-02 +80 20:57:04.454][11862, 44][temporary-6][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 20:57:04.455][11862, 39][temporary-3][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:57:04.455][11862, 39][temporary-3][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:57:04.459][11862, 42][temporary-4][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:57:04.459][11862, 42][temporary-4][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:57:04.463][11862, 1*][main][daemon][SystemEventManager][][apn changed: WIFI -> NO_NETWORK, notifyDisconnected, from:BroadcastReceiver
[I][2025-04-02 +80 20:57:04.464][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_ON
[I][2025-04-02 +80 20:57:04.472][11862, 45][temporary-7][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:57:04.474][11862, 42][temporary-4][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:57:04.485][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=ACTION_ON_DISCONNECTED
[I][2025-04-02 +80 20:57:04.575][11862, 304][Thread_TimerJobQueue][daemon][wise_download][][traceId:0 msg:com.tencent.pangu.module.timer.job.AutoDownloadTimerJob work~~~ 
[I][2025-04-02 +80 20:57:04.578][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=ACTION_ON_TIME_POINT
[W][2025-04-02 +80 20:57:05.907][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:05.909][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:57:07.120][11862, 220][LogProcessService-7][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:57:07.125][11862, 220][LogProcessService-7][daemon][HttpNetWorkTaskV2][][[284]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:284,useHttp2:false
[I][2025-04-02 +80 20:57:07.169][11862, 427][protocalManagerStatReport-427][daemon][HttpNetWorkTaskV2][][[284]callStart
[I][2025-04-02 +80 20:57:07.170][11862, 427][protocalManagerStatReport-427][daemon][HttpNetWorkTaskV2][][[284]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:57:07.172][11862, 427][protocalManagerStatReport-427][daemon][HttpNetWorkTaskV2][][[284]connectStart
[I][2025-04-02 +80 20:57:07.251][11862, 427][protocalManagerStatReport-427][daemon][HttpNetWorkTaskV2][][[284]connectEnd protocol:http/1.1
[I][2025-04-02 +80 20:57:07.251][11862, 427][protocalManagerStatReport-427][daemon][HttpNetWorkTaskV2][][[284]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:57:07.348][11862, 427][protocalManagerStatReport-427][daemon][HttpNetWorkTaskV2][][[284]callEnd
[I][2025-04-02 +80 20:57:07.363][11862, 427][protocalManagerStatReport-427][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-02 +80 20:57:07.447][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:07.449][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:57:08.946][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:08.949][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:57:10.446][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:10.448][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:57:10.554][11862, 1*][main][daemon][SystemEventManager][][apn changed: NO_NETWORK -> WIFI, notifyConnected, from:BroadcastReceiver
[I][2025-04-02 +80 20:57:10.554][11862, 45][temporary-7][daemon][GDTDM.GlobalAmsDownloadManager][][onConnected, apn: WIFI
[I][2025-04-02 +80 20:57:10.555][11862, 1*][main][daemon][GDTDM.GlobalAmsDownloadManager][][notify onWifiConnected. entries: 0
[I][2025-04-02 +80 20:57:10.558][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=ACTION_ON_CONNECTED
[W][2025-04-02 +80 20:57:10.599][11862, 46][temporary-8][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:57:10.600][11862, 46][temporary-8][daemon][HttpNetWorkTaskV2][][[285]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:285,useHttp2:false
[I][2025-04-02 +80 20:57:10.601][11862, 46][temporary-8][daemon][GetAppUpdateEntranceManager][][sendRequest
[I][2025-04-02 +80 20:57:10.615][11862, 429][protocalManager-429][daemon][HttpNetWorkTaskV2][][[285]callStart
[I][2025-04-02 +80 20:57:10.616][11862, 429][protocalManager-429][daemon][HttpNetWorkTaskV2][][[285]dnsStart domainName:mayybnew.3g.qq.com
[W][2025-04-02 +80 20:57:10.713][11862, 29][Binder:11862_3][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:57:10.715][11862, 29][Binder:11862_3][daemon][HttpNetWorkTaskV2][][[286]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:286,useHttp2:false
[I][2025-04-02 +80 20:57:10.726][11862, 427][protocalManager-427][daemon][HttpNetWorkTaskV2][][[286]callStart
[I][2025-04-02 +80 20:57:10.727][11862, 427][protocalManager-427][daemon][HttpNetWorkTaskV2][][[286]dnsStart domainName:mayybnew.3g.qq.com
[W][2025-04-02 +80 20:57:10.748][11862, 46][temporary-8][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:57:10.785][11862, 46][temporary-8][daemon][HttpNetWorkTaskV2][][[287]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:287,useHttp2:false
[I][2025-04-02 +80 20:57:10.794][11862, 431][protocalManager-431][daemon][HttpNetWorkTaskV2][][[287]callStart
[I][2025-04-02 +80 20:57:10.795][11862, 431][protocalManager-431][daemon][HttpNetWorkTaskV2][][[287]dnsStart domainName:mayybnew.3g.qq.com
[E][2025-04-02 +80 20:57:10.974][11862, 45][temporary-7][daemon][CkCoreManager][][wrapper is not valid, bid:40, version:4, frequency:10800000, hasResource:true, checkTime:1743403384674
[I][2025-04-02 +80 20:57:11.296][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------onActionIntercept------yyb.intent.action.WIFI_CONNECTED
[I][2025-04-02 +80 20:57:11.300][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateConfig----dailyCount--6--frequencyControl--600
[I][2025-04-02 +80 20:57:11.306][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions------1012
[I][2025-04-02 +80 20:57:11.306][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions---item---1, 5
[I][2025-04-02 +80 20:57:11.306][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions---item---1, 7
[I][2025-04-02 +80 20:57:11.306][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions---item---1, 10
[I][2025-04-02 +80 20:57:11.307][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions---item---1, 100
[I][2025-04-02 +80 20:57:11.307][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions---item---1, 13
[I][2025-04-02 +80 20:57:11.307][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions---item---1, 14
[I][2025-04-02 +80 20:57:11.307][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions---item---1, 24
[I][2025-04-02 +80 20:57:11.307][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions---item---1, 32
[I][2025-04-02 +80 20:57:11.307][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:------updateActions---end---8,[, , , , , , , ]
[I][2025-04-02 +80 20:57:11.352][11862, 429][protocalManager-429][daemon][HttpDnsImpl][][domain:mayybnew.3g.qq.com lookupAll:[]
[I][2025-04-02 +80 20:57:11.353][11862, 429][protocalManager-429][daemon][CustomDnsTab][][localDnsRetryMaxCost:10000
[I][2025-04-02 +80 20:57:11.353][11862, 429][protocalManager-429][daemon][CustomDnsTab][][localDnsRetryMaxNum:0
[I][2025-04-02 +80 20:57:11.354][11862, 429][protocalManager-429][daemon][CustomDnsTab][][localDnsRetryInterval:200
[I][2025-04-02 +80 20:57:11.354][11862, 429][protocalManager-429][daemon][CustomDnsTab][][enableDnsRetryV2:false
[I][2025-04-02 +80 20:57:11.356][11862, 427][protocalManager-427][daemon][HttpNetWorkTaskV2][][[286]callFailed
[I][2025-04-02 +80 20:57:11.356][11862, 431][protocalManager-431][daemon][HttpNetWorkTaskV2][][[287]callFailed
[E][2025-04-02 +80 20:57:11.357][11862, 431][protocalManager-431][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetAppUpdateEntrance
[E][2025-04-02 +80 20:57:11.362][11862, 427][protocalManager-427][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetAppUpdateEntrance
[E][2025-04-02 +80 20:57:11.362][11862, 427][protocalManager-427][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetAppUpdateEntrance
[I][2025-04-02 +80 20:57:11.364][11862, 429][protocalManager-429][daemon][HttpNetWorkTaskV2][][[285]callFailed
[E][2025-04-02 +80 20:57:11.364][11862, 429][protocalManager-429][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetAppUpdate
[E][2025-04-02 +80 20:57:11.364][11862, 429][protocalManager-429][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetAppUpdate
[E][2025-04-02 +80 20:57:11.379][11862, 431][protocalManager-431][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetAppUpdateEntrance
[I][2025-04-02 +80 20:57:11.430][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:check font with cover current: false, feature : 0
[I][2025-04-02 +80 20:57:11.444][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:#ignoreBySwitch: busiType=5, reachType=1
[I][2025-04-02 +80 20:57:11.556][11862, 37][temporary-2][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 20:57:11.558][11862, 37][temporary-2][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ebdd55d u0a3080 TPSL bg:+26m47s375ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.pangu.download.AppDownloadMiddleResolver.isInstallQueueIdle(ProGuard:131)
	at yyb8922819.x60.xe.d(ProGuard:15)
	at yyb8922819.x60.xc.e(ProGuard:105)
	at yyb8922819.z60.xb.c(ProGuard:30)
	at yyb8922819.z60.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922819.tf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 20:57:11.561][11862, 37][temporary-2][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-02 +80 20:57:11.577][11862, 37][temporary-2][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ebdd55d u0a3080 TPSL bg:+26m47s380ms idle change:cached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.pangu.download.AppDownloadMiddleResolver.isInstallQueueIdle(ProGuard:131)
	at yyb8922819.x60.xe.d(ProGuard:15)
	at yyb8922819.x60.xc.e(ProGuard:105)
	at yyb8922819.z60.xb.c(ProGuard:30)
	at yyb8922819.z60.xd.onActionIntercept(ProGuard:170)
	at com.tencent.pangu.intent.interceptor.xb$xc.c(ProGuard:102)
	at com.tencent.pangu.intent.interceptor.BroadcastInterceptReceiver$xb.run(ProGuard:32)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922819.tf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 20:57:11.581][11862, 37][temporary-2][daemon][MainBinderManager][][queryBinder, binderCode : 1008 binderManager:false binder:false
[E][2025-04-02 +80 20:57:11.582][11862, 37][temporary-2][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1008
[I][2025-04-02 +80 20:57:11.582][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:--getMsgInfo---AppUpdateDeskMsg
[I][2025-04-02 +80 20:57:11.583][11862, 37][temporary-2][daemon][WildToolbarDataManager][][getToolbarClickEvent funcId = 
[I][2025-04-02 +80 20:57:11.583][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:#ignoreBySwitch: busiType=7, reachType=1
[I][2025-04-02 +80 20:57:11.603][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:--getMsgInfo---OptDeskMsg
[I][2025-04-02 +80 20:57:11.605][11862, 37][temporary-2][daemon][WildToolbarDataManager][][getToolbarClickEvent funcId = 
[I][2025-04-02 +80 20:57:11.605][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:#ignoreBySwitch: busiType=10, reachType=1
[I][2025-04-02 +80 20:57:11.653][11862, 37][temporary-2][daemon][WildToolbarDataManager][][getToolbarClickEvent funcId = 
[I][2025-04-02 +80 20:57:11.653][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:#ignoreBySwitch: busiType=100, reachType=1
[I][2025-04-02 +80 20:57:11.658][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:--getMsgInfo---CommonDeskMsg
[I][2025-04-02 +80 20:57:11.658][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:#ignoreBySwitch: busiType=13, reachType=1
[I][2025-04-02 +80 20:57:11.663][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:--DispatchToRubbishDeskMsg---ym, size:0
[I][2025-04-02 +80 20:57:11.664][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:VideoAppRubbishDeskMsg---size--fail----cacheMBSize = 0 , singleAppMBSize = 0
[I][2025-04-02 +80 20:57:11.664][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:#ignoreBySwitch: busiType=14, reachType=1
[I][2025-04-02 +80 20:57:11.682][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:--getMsgInfo---CommonDeskMsg
[I][2025-04-02 +80 20:57:11.682][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:#ignoreBySwitch: busiType=24, reachType=1
[I][2025-04-02 +80 20:57:11.690][11862, 37][temporary-2][daemon][WildToolbarDataManager][][getToolbarClickEvent funcId = 
[I][2025-04-02 +80 20:57:11.690][11862, 37][temporary-2][daemon][BatteryDeskMsg][][#getMsgInfo: params = {currentBatteryLevel=100}
[I][2025-04-02 +80 20:57:11.690][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:#ignoreBySwitch: busiType=32, reachType=1
[I][2025-04-02 +80 20:57:11.696][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:--getMsgInfo---CommonDeskMsg
[I][2025-04-02 +80 20:57:11.702][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:IManager-run--getList,size:7
[I][2025-04-02 +80 20:57:11.702][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][PersonalizedMessageDataManager#sendRequest eventCode=1012
[I][2025-04-02 +80 20:57:11.704][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:PersonalizedMessageDataManager#sendRequest eventCode=1012
[I][2025-04-02 +80 20:57:11.714][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:PersonalizedRequestPreHandler: getFilterReachBusinessItems, config empty
[I][2025-04-02 +80 20:57:11.715][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:PersonalizedRequestPreHandler: filterUnimportantMessage, config empty, just return reqList
[I][2025-04-02 +80 20:57:11.716][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][PersonalizedMessageEngine#sendRequest eventCode=1012
[I][2025-04-02 +80 20:57:11.716][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][GetPushAndPopupSystemMsgRequest#createRequest size=7
[I][2025-04-02 +80 20:57:11.716][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:---createRequest---eventType---1012
[I][2025-04-02 +80 20:57:11.716][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 20:57:11.716][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:-createRequest-reach:1,buss:7,params:{}
[I][2025-04-02 +80 20:57:11.717][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 20:57:11.718][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.719][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.719][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.763][11862, 37][temporary-2][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.tencent.tmgp.pubgmhd, channelId = 10033016 costTime = 44
[I][2025-04-02 +80 20:57:11.774][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.830][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.830][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.850][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.850][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.852][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.852][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.853][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.853][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.854][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.854][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.855][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.855][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.856][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.856][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.857][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.857][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.858][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.858][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.859][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.859][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.860][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.860][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.861][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.861][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.862][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.862][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.863][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.863][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.864][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.864][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.865][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.865][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.866][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.866][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.867][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.867][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.868][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.868][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.869][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.870][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.870][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.871][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.871][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.872][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.872][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.873][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.873][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.874][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.874][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.875][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.876][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.876][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.877][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.877][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.878][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.878][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.879][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.879][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.880][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.880][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.881][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.881][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.882][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.882][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.883][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.883][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.884][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.884][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.885][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.885][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.886][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.886][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.887][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.887][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.888][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.888][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.889][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.889][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.890][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.890][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.891][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.891][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.892][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.892][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.893][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.893][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.897][11862, 37][temporary-2][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.tencent.tmgp.dnf, channelId = 10048772 costTime = 4
[I][2025-04-02 +80 20:57:11.898][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.899][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.902][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.905][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.905][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.916][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.916][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.926][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.927][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.928][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.928][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.929][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.929][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.929][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.929][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.930][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.930][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.931][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.931][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.932][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.932][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.933][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.933][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.934][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.934][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.935][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.935][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.936][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.936][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.937][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.937][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.938][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.938][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.939][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.939][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.940][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.940][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.941][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.942][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.942][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.942][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.944][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.944][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[W][2025-04-02 +80 20:57:11.946][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:11.947][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:57:11.948][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.948][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.949][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.949][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.950][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.950][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.951][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.951][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.952][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.952][11862, 37][temporary-2][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:57:11.953][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson][][packageInfo==null
[I][2025-04-02 +80 20:57:11.956][11862, 37][temporary-2][daemon][ReturnGiftChannelUtilJson updateInstalledStatus][][{"gameInfos":[{"channelid":"10033016","pkgName":"com.tencent.tmgp.pubgmhd","versionCode":-1},{"channelid":"10048772","pkgName":"com.tencent.tmgp.dnf","versionCode":-1}],"isGameInfoReady":true}
[I][2025-04-02 +80 20:57:11.958][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:-createRequest-reach:1,buss:32,params:{return_gift_game_info={"gameInfos":[{"channelid":"10033016","pkgName":"com.tencent.tmgp.pubgmhd","versionCode":-1},{"channelid":"10048772","pkgName":"com.tencent.tmgp.dnf","versionCode":-1}],"isGameInfoReady":true}}
[I][2025-04-02 +80 20:57:11.958][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 20:57:11.958][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:-createRequest-reach:1,buss:10,params:{storagePermission=false}
[I][2025-04-02 +80 20:57:11.958][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 20:57:11.959][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:-createRequest-reach:1,buss:100,params:{}
[I][2025-04-02 +80 20:57:11.959][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 20:57:11.959][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:-createRequest-reach:1,buss:24,params:{currentBatteryLevel=100}
[I][2025-04-02 +80 20:57:11.959][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 20:57:11.959][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:-createRequest-reach:1,buss:5,params:{}
[I][2025-04-02 +80 20:57:11.959][11862, 37][temporary-2][daemon][PreUpdateAppEngine][][addPreUpdateDownloadInfo eventCode=1010
[I][2025-04-02 +80 20:57:11.960][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:-createRequest-reach:1,buss:14,params:{}
[W][2025-04-02 +80 20:57:11.961][11862, 37][temporary-2][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:57:11.962][11862, 37][temporary-2][daemon][HttpNetWorkTaskV2][][[288]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:288,useHttp2:false
[I][2025-04-02 +80 20:57:11.977][11862, 432][protocalManager-432][daemon][HttpNetWorkTaskV2][][[288]callStart
[I][2025-04-02 +80 20:57:11.977][11862, 432][protocalManager-432][daemon][HttpNetWorkTaskV2][][[288]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 20:57:11.978][11862, 432][protocalManager-432][daemon][HttpNetWorkTaskV2][][[288]callFailed
[E][2025-04-02 +80 20:57:11.979][11862, 432][protocalManager-432][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetPushAndPopupSystemMsg
[E][2025-04-02 +80 20:57:11.979][11862, 432][protocalManager-432][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetPushAndPopupSystemMsg
[I][2025-04-02 +80 20:57:11.982][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:markEventTriggerRequest 1012
[I][2025-04-02 +80 20:57:11.988][11862, 37][temporary-2][daemon][FLog_TouchSysInterceptor][][[temporary-2]TouchSysInterceptor:PersonalizedMessageDataManager#sendRequest success:20250402, 1743598631988
[I][2025-04-02 +80 20:57:13.427][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[286]callStart
[I][2025-04-02 +80 20:57:13.428][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[286]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 20:57:13.435][11862, 437][protocalManager-437][daemon][HttpNetWorkTaskV2][][[285]callStart
[I][2025-04-02 +80 20:57:13.436][11862, 437][protocalManager-437][daemon][HttpNetWorkTaskV2][][[285]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 20:57:13.436][11862, 427][protocalManager-427][daemon][HttpNetWorkTaskV2][][[287]callStart
[I][2025-04-02 +80 20:57:13.437][11862, 427][protocalManager-427][daemon][HttpNetWorkTaskV2][][[287]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 20:57:13.438][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[286]callFailed
[I][2025-04-02 +80 20:57:13.439][11862, 437][protocalManager-437][daemon][HttpNetWorkTaskV2][][[285]callFailed
[E][2025-04-02 +80 20:57:13.439][11862, 435][protocalManager-435][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetAppUpdateEntrance
[E][2025-04-02 +80 20:57:13.439][11862, 437][protocalManager-437][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetAppUpdate
[E][2025-04-02 +80 20:57:13.439][11862, 435][protocalManager-435][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetAppUpdateEntrance
[E][2025-04-02 +80 20:57:13.440][11862, 437][protocalManager-437][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetAppUpdate
[I][2025-04-02 +80 20:57:13.441][11862, 427][protocalManager-427][daemon][HttpNetWorkTaskV2][][[287]callFailed
[E][2025-04-02 +80 20:57:13.441][11862, 427][protocalManager-427][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetAppUpdateEntrance
[E][2025-04-02 +80 20:57:13.442][11862, 427][protocalManager-427][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetAppUpdateEntrance
[W][2025-04-02 +80 20:57:13.446][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:13.448][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:57:14.213][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 450263219 , reportEventTime = 450273258
[I][2025-04-02 +80 20:57:14.872][11862, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=100
[W][2025-04-02 +80 20:57:14.940][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:14.942][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:57:15.385][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_OFF
[I][2025-04-02 +80 20:57:16.181][11862, 1*][main][daemon][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[W][2025-04-02 +80 20:57:16.447][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:16.451][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:57:17.948][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:57:17.951][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:58:55.114][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:58:55.116][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: report freeze. diffMs = 90902 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 100902 , taskStartElapsedRealtimeMs = 450273259 , reportEventTime = 450374161
[I][2025-04-02 +80 20:58:55.120][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: duration = 100902 , taskStartDate = 20250402 , currentDate = 20250402
[I][2025-04-02 +80 20:58:55.121][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: [20250402]-100902 , durationMs = 100902
[I][2025-04-02 +80 20:58:55.155][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:58:55.156][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:58:55.172][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 20:58:55.173][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 20:58:55.173][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 20:58:55.174][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 20:58:55.174][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 20:58:55.175][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 20:58:55.193][11862, 37][temporary-2][daemon][FLog_login_log][][[temporary-2]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:58:55.197][11862, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=99
[I][2025-04-02 +80 20:58:55.202][11862, 43][temporary-5][daemon][FLog_login_log][][[temporary-5]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:58:55.274][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:58:55.274][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:58:55.278][11862, 35][temporary-1][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:58:55.473][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_ON
[W][2025-04-02 +80 20:58:55.801][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:58:55.804][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:58:57.307][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:58:57.310][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[W][2025-04-02 +80 20:58:58.148][11862, 135][LogProcessService-3][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:58:58.150][11862, 135][LogProcessService-3][daemon][HttpNetWorkTaskV2][][[289]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:289,useHttp2:false
[I][2025-04-02 +80 20:58:58.174][11862, 438][protocalManagerStatReport-438][daemon][HttpNetWorkTaskV2][][[289]callStart
[I][2025-04-02 +80 20:58:58.175][11862, 438][protocalManagerStatReport-438][daemon][HttpNetWorkTaskV2][][[289]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:58:58.178][11862, 438][protocalManagerStatReport-438][daemon][HttpNetWorkTaskV2][][[289]connectStart
[W][2025-04-02 +80 20:58:58.807][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:58:58.809][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:59:00.027][11862, 39][temporary-3][daemon][FLog_login_log][][[temporary-3]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 20:59:00.201][11862, 29][Binder:11862_3][daemon][Tinker.ProcessLifecycle][][App进前台检查:activityCount=1 , isAlreadyVisible=false
[I][2025-04-02 +80 20:59:00.207][11862, 29][Binder:11862_3][daemon][Tinker.ProcessLifecycle][][【应用宝】进程market 进入前台，进入时间=1743598740201
[I][2025-04-02 +80 20:59:00.210][11862, 1*][main][daemon][DualDownloadApkManager][][handleUIEvent { when=-2d15h8m20s484ms realwhen=0 isVsync=false isAsynchronous=false what=1519 target=yyb8922819.w5.xh$xc }
[I][2025-04-02 +80 20:59:00.212][11862, 1*][main][daemon][DualDownloadApkManager][][sim false
[I][2025-04-02 +80 20:59:00.250][11862, 104][Binder:11862_4][daemon][Tinker.ProcessLifecycle][][App进前台检查:activityCount=2 , isAlreadyVisible=true
[I][2025-04-02 +80 20:59:00.252][11862, 1*][main][daemon][DualDownloadApkManager][][handleUIEvent { when=-2d15h8m20s526ms realwhen=0 isVsync=false isAsynchronous=false what=1519 target=yyb8922819.w5.xh$xc }
[I][2025-04-02 +80 20:59:00.253][11862, 1*][main][daemon][DualDownloadApkManager][][sim false
[I][2025-04-02 +80 20:59:00.254][11862, 104][Binder:11862_4][daemon][UserStateReporter][][onAppStateChangeToMainThread: isFront = true, currentPageId = 2035, isAppFront = false
[I][2025-04-02 +80 20:59:00.254][11862, 1*][main][daemon][UserStateReporter][][onAppStateChange: isFront = true , currentPageId = 2035 , isAppFront = false
[I][2025-04-02 +80 20:59:00.254][11862, 1*][main][daemon][UserStateReporter][][onAppStateChange: app to foreground<<<< currentPageId = 2035
[I][2025-04-02 +80 20:59:00.254][11862, 1*][main][daemon][UserStateReporter][][startHeartbeatReport: called start.
[I][2025-04-02 +80 20:59:00.254][11862, 1*][main][daemon][UserStateReporter][][getHeartbeatReportRecord: inDayReportRecordStr = 1743523200000|21|0
[I][2025-04-02 +80 20:59:00.255][11862, 1*][main][daemon][UserStateReporter][][getHeartbeatReportRecord: time is valid 1743523200000|21|0
[I][2025-04-02 +80 20:59:00.255][11862, 1*][main][daemon][UserStateReporter][][startHeartbeatReport: today over times, reportRecord = HeartbeatReportRecord(times=21, preReportStayDuration=0)
[I][2025-04-02 +80 20:59:00.255][11862, 1*][main][daemon][UserStateReporter][][onAppStateChange: isAppFront = true , currentPageId = 2035
[I][2025-04-02 +80 20:59:00.255][11862, 1*][main][daemon][GlobalMonitor][][app go foreground, process:daemon
[I][2025-04-02 +80 20:59:00.256][11862, 1*][main][daemon][RdeliveryConfigFetcher][][从后台切到前台
[I][2025-04-02 +80 20:59:00.256][11862, 1*][main][daemon][PackageMonitorManager][][进入前台1...
[I][2025-04-02 +80 20:59:00.258][11862, 35][temporary-1][daemon][PackageMonitorManager][][get installed packages from OSPackageManager
[E][2025-04-02 +80 20:59:00.268][11862, 44][temporary-6][daemon][RightlySDKManager_PandoraEx.LocationMonitor][][WM#G_CON_INFO is Really Call System API
[I][2025-04-02 +80 20:59:00.275][11862, 104][Binder:11862_4][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:59:00.279][11862, 104][Binder:11862_4][daemon][LogTunnel][][processUAXXX 1
[E][2025-04-02 +80 20:59:00.279][11862, 35][temporary-1][daemon][PackageMonitorManager][][reportDiffOnAppChanged 获取安装列表失败
[I][2025-04-02 +80 20:59:00.280][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.280][11862, 104][Binder:11862_4][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:59:00.282][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.ss.android.ugc.aweme, channelId = null costTime = 2
[I][2025-04-02 +80 20:59:00.283][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.287][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.tencent.mm, channelId = null costTime = 5
[I][2025-04-02 +80 20:59:00.287][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.291][11862, 29][Binder:11862_3][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:59:00.291][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.tencent.tmgp.dnf, channelId = 10048772 costTime = 4
[I][2025-04-02 +80 20:59:00.291][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[W][2025-04-02 +80 20:59:00.292][11862, 29][Binder:11862_3][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:59:00.292][11862, 29][Binder:11862_3][daemon][HttpNetWorkTaskV2][][[290]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:290,useHttp2:false
[I][2025-04-02 +80 20:59:00.294][11862, 104][Binder:11862_4][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:59:00.300][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.android.xgnwcsgj, channelId = null costTime = 9
[I][2025-04-02 +80 20:59:00.301][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.302][11862, 439][protocalManagerStatReport-439][daemon][HttpNetWorkTaskV2][][[290]callStart
[I][2025-04-02 +80 20:59:00.302][11862, 439][protocalManagerStatReport-439][daemon][HttpNetWorkTaskV2][][[290]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-02 +80 20:59:00.302][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.netease.pes.mi, channelId = null costTime = 1
[I][2025-04-02 +80 20:59:00.303][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.304][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = ca.zgrs.clipper, channelId = null costTime = 1
[I][2025-04-02 +80 20:59:00.304][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[W][2025-04-02 +80 20:59:00.306][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:59:00.307][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:59:00.311][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.xiaomi.gamecenter, channelId = null costTime = 7
[I][2025-04-02 +80 20:59:00.311][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.316][11862, 439][protocalManagerStatReport-439][daemon][CustomDns-mayybstatnew.3g.qq.com][][lookupImpl ret:[/************, /*************]
[I][2025-04-02 +80 20:59:00.316][11862, 439][protocalManagerStatReport-439][daemon][HttpNetWorkTaskV2][][[290]connectStart
[I][2025-04-02 +80 20:59:00.325][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.github.uiautomator, channelId = null costTime = 14
[I][2025-04-02 +80 20:59:00.325][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.332][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.vivo.easyshare, channelId = null costTime = 7
[I][2025-04-02 +80 20:59:00.332][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.333][11862, 439][protocalManagerStatReport-439][daemon][HttpNetWorkTaskV2][][[290]connectEnd protocol:http/1.1
[I][2025-04-02 +80 20:59:00.334][11862, 439][protocalManagerStatReport-439][daemon][HttpNetWorkTaskV2][][[290]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:59:00.340][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.tencent.android.qqdownloader, channelId = null costTime = 8
[I][2025-04-02 +80 20:59:00.340][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.350][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.tencent.tmgp.pubgmhd, channelId = 10033016 costTime = 10
[I][2025-04-02 +80 20:59:00.351][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.358][11862, 43][temporary-5][daemon][xb][][receive native user present event
[I][2025-04-02 +80 20:59:00.361][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.USER_PRESENT
[I][2025-04-02 +80 20:59:00.362][11862, 37][temporary-2][daemon][NewBackGroundScanManager][][[galtest] start scan
[W][2025-04-02 +80 20:59:00.370][11862, 37][temporary-2][daemon][NewBackGroundScanManager][][未赋予存储权限
[I][2025-04-02 +80 20:59:00.363][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = myworkspace.mobile.clients.android, channelId = null costTime = 12
[I][2025-04-02 +80 20:59:00.366][11862, 439][protocalManagerStatReport-439][daemon][HttpNetWorkTaskV2][][[290]callEnd
[I][2025-04-02 +80 20:59:00.379][11862, 35][temporary-1][daemon][PackageManagerService][][get installed package from OSPackageManager
[I][2025-04-02 +80 20:59:00.379][11862, 439][protocalManagerStatReport-439][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:00.399][11862, 35][temporary-1][daemon][ChannelUtilInQQDownloader][][read chanel pkg = com.example.myapplication, channelId = null costTime = 20
[I][2025-04-02 +80 20:59:00.399][11862, 35][temporary-1][daemon][PackageMonitorManager][][reportDiffPackageList, add size=0, update size=0, remove size=0
[I][2025-04-02 +80 20:59:00.600][11862, 45][temporary-7][daemon][EventTransformInterceptor][][TransformAction: source = android.intent.action.USER_PRESENT, target = yyb.intent.action.SCREEN_AWAKE
[I][2025-04-02 +80 20:59:00.605][11862, 44][temporary-6][daemon][FLog_TouchSysInterceptor][][[temporary-6]TouchSysInterceptor:------onActionIntercept------yyb.intent.action.SCREEN_AWAKE
[I][2025-04-02 +80 20:59:00.606][11862, 44][temporary-6][daemon][FLog_TouchSysInterceptor][][[temporary-6]TouchSysInterceptor:------updateConfig----dailyCount--6--frequencyControl--600
[W][2025-04-02 +80 20:59:00.608][11862, 44][temporary-6][daemon][FLog_TouchSysInterceptor][][TouchSysInterceptor:yyb.intent.action.SCREEN_AWAKE ------inSilentTime---frequency----true, false
[I][2025-04-02 +80 20:59:00.724][11862, 411][Binder:11862_E][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:59:00.840][11862, 39][temporary-3][daemon][ProtocolReportUtilsNew][][failRatiosConfig:
        {"def_ratio":10, ",24,":50}
    
[I][2025-04-02 +80 20:59:00.903][11862, 435][net_dedicated-435][daemon][FLog_TouchSysInterceptor][][[net_dedicated-435]TouchSysInterceptor:PersonalizedMessageDataManager#onDataFailed: event=1012, itemsInfo=busiType=7, reachType=1; busiType=32, reachType=1; busiType=10, reachType=1; busiType=100, reachType=1; busiType=24, reachType=1; busiType=5, reachType=1; busiType=14, reachType=1; 
[E][2025-04-02 +80 20:59:00.903][11862, 435][net_dedicated-435][daemon][FLog_TouchSysInterceptor][][TouchSysInterceptor:--TouchSysEngine--onDataFailed--:-828
[I][2025-04-02 +80 20:59:00.918][11862, 65][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:59:00.920][11862, 72][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:59:00.923][11862, 68][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:59:00.942][11862, 1*][main][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 20:59:00.942][11862, 1*][main][daemon][floatingwindow][][sendToolbarRequestFormUpdateList: switch close.
[I][2025-04-02 +80 20:59:00.943][11862, 39][temporary-3][daemon][ToolbarRequest][][sendRequest from:APP_UPDATE_LIST, process:daemon
[I][2025-04-02 +80 20:59:00.948][11862, 39][temporary-3][daemon][rubbishSelectedSize][][getDeepScanSelectedCacheSize=0
[I][2025-04-02 +80 20:59:00.949][11862, 46][temporary-8][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:00.949][11862, 46][temporary-8][daemon][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-02 +80 20:59:00.961][11862, 39][temporary-3][daemon][FLog_TouchSysInterceptor][][[temporary-3]TouchSysInterceptor:getDeskShowEvent funcId = 
[I][2025-04-02 +80 20:59:00.961][11862, 39][temporary-3][daemon][OptimizeManager][][使用新的打分规则统计
[I][2025-04-02 +80 20:59:00.961][11862, 39][temporary-3][daemon][NewPhoneOptimizeManager][][没有存储权限，分数为30
[I][2025-04-02 +80 20:59:00.961][11862, 39][temporary-3][daemon][OptimizeManager][][calcFinalScore finalScore = 30
[I][2025-04-02 +80 20:59:00.963][11862, 39][temporary-3][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 20:59:00.971][11862, 39][temporary-3][daemon][GetWildToolbarEngine][][#getRequestMap: requestMap = {displayPushList={}, busiTypeExposure=, remainSdcardSize=77234950144, rubbishSize=0, totalMem=7468, pendingInstallCount=0, bigFileSize=0, weComRubbishSize=0, freeMem=4503, videoSize=0, pendingInstallAppIDs=, wxSize=0, score=30, contentCreatedTime=, qqSize=0, baiduRubbishSize=0, storagePermission=false, dingTalkRubbishSize=0, updateSize=13, imageSize=0}
[I][2025-04-02 +80 20:59:00.977][11862, 39][temporary-3][daemon][ToolbarRequest][][doSendRequest, from:APP_UPDATE_LIST, process:daemon
[W][2025-04-02 +80 20:59:00.977][11862, 39][temporary-3][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:59:00.978][11862, 39][temporary-3][daemon][HttpNetWorkTaskV2][][[291]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:291,useHttp2:false
[I][2025-04-02 +80 20:59:00.981][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[291]callStart
[I][2025-04-02 +80 20:59:00.981][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[291]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 20:59:00.996][11862, 435][protocalManager-435][daemon][CustomDns-mayybnew.3g.qq.com][][lookupImpl ret:[/**************, /**************]
[I][2025-04-02 +80 20:59:00.996][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[291]connectStart
[I][2025-04-02 +80 20:59:01.020][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[291]connectEnd protocol:http/1.1
[I][2025-04-02 +80 20:59:01.020][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[291]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[W][2025-04-02 +80 20:59:01.191][11862, 220][LogProcessService-7][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:59:01.194][11862, 220][LogProcessService-7][daemon][HttpNetWorkTaskV2][][[292]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:292,useHttp2:false
[I][2025-04-02 +80 20:59:01.206][11862, 437][protocalManagerStatReport-437][daemon][HttpNetWorkTaskV2][][[292]callStart
[I][2025-04-02 +80 20:59:01.207][11862, 437][protocalManagerStatReport-437][daemon][HttpNetWorkTaskV2][][[292]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:59:01.242][11862, 437][protocalManagerStatReport-437][daemon][HttpNetWorkTaskV2][][[292]callEnd
[I][2025-04-02 +80 20:59:01.248][11862, 437][protocalManagerStatReport-437][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:01.270][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[291]callEnd
[I][2025-04-02 +80 20:59:01.279][11862, 438][protocalManagerStatReport-438][daemon][HttpNetWorkTaskV2][][[289]connectEnd protocol:http/1.1
[I][2025-04-02 +80 20:59:01.279][11862, 438][protocalManagerStatReport-438][daemon][HttpNetWorkTaskV2][][[289]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:59:01.281][11862, 435][protocalManager-435][daemon][FLog_login_log][][[protocalManager-435]LoginProxy:setIdentityInfo
needTofresh:false
processName:daemon
[I][2025-04-02 +80 20:59:01.282][11862, 1*][main][daemon][FLog_login_log][][[main]LoginProxy:id = com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849, processId = 3,needRefresh = false, currProcess = daemon
[I][2025-04-02 +80 20:59:01.288][11862, 435][protocalManager-435][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:01.288][11862, 435][protocalManager-435][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:01.289][11862, 435][protocalManager-435][daemon][FLog_login_log][][[protocalManager-435]LoginProxy:id com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849isNeedSendLoginSucMsgfalse
[I][2025-04-02 +80 20:59:01.290][11862, 435][protocalManager-435][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:01.298][11862, 44][temporary-6][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:01.298][11862, 44][temporary-6][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:01.300][11862, 44][temporary-6][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:59:01.303][11862, 46][temporary-8][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 20:59:01.322][11862, 46][temporary-8][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 20:59:01.335][11862, 46][temporary-8][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 20:59:01.344][11862, 46][temporary-8][daemon][InstallNotificationAction][][server config = 
[I][2025-04-02 +80 20:59:01.344][11862, 46][temporary-8][daemon][InstallNotificationAction][][config json = {"package_name":"com.tencent.tmgp.dnf","start_period": 1716220800000,"end_period": 1716825600000,"title": "已下载完成","sub_title": "立即安装，开始游戏 >"}
[I][2025-04-02 +80 20:59:01.344][11862, 46][temporary-8][daemon][InstallNotificationAction][][getConfig packageName = com.tencent.tmgp.dnf;start=1716220800000;endTIme = 1716825600000
[W][2025-04-02 +80 20:59:01.344][11862, 46][temporary-8][daemon][InstallNotificationAction][][com.tencent.tmgp.dnf;不在时间范围内
[I][2025-04-02 +80 20:59:01.344][11862, 46][temporary-8][daemon][InstallNotificationAction][][不满足服务器配置
[I][2025-04-02 +80 20:59:01.345][11862, 46][temporary-8][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 20:59:01.345][11862, 46][temporary-8][daemon][WildToolbarNotification][][sendEventForceRefresh: canShowRecommendNewUi = false , needRecreateToolbar = false
[I][2025-04-02 +80 20:59:01.364][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-02 +80 20:59:01.364][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-02 +80 20:59:01.364][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-02 +80 20:59:01.364][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-02 +80 20:59:01.364][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-02 +80 20:59:01.365][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 20:59:01.449][11862, 438][protocalManagerStatReport-438][daemon][HttpNetWorkTaskV2][][[289]callEnd
[I][2025-04-02 +80 20:59:01.454][11862, 438][protocalManagerStatReport-438][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:01.495][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 20:59:01.495][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 20:59:01.495][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 20:59:01.496][11862, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=6
[I][2025-04-02 +80 20:59:01.498][11862, 45][temporary-7][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 20:59:01.523][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 20:59:01.523][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 20:59:01.523][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 20:59:01.523][11862, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=6
[I][2025-04-02 +80 20:59:01.525][11862, 43][temporary-5][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 20:59:01.539][11862, 351][Binder:11862_B][daemon][LogTunnel][][processUAXXX 1
[W][2025-04-02 +80 20:59:01.767][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:59:01.770][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:59:02.300][11862, 351][Binder:11862_B][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:59:02.411][11862, 351][Binder:11862_B][daemon][ionia_event_WallpaperVisibilityObserver][][onWallpaperVisibilityRealChanged, visible : true, displayId : 0
[E][2025-04-02 +80 20:59:02.543][11862, 1*][main][daemon][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
[I][2025-04-02 +80 20:59:02.543][11862, 1*][main][daemon][xb][][onReceiveNativeBroadcast: android.intent.action.CLOSE_SYSTEM_DIALOGS
[I][2025-04-02 +80 20:59:02.614][11862, 303][worker][daemon][ionia_event_WallpaperVisibilityObserver][][onWallpaperVisibilityChanged, visible : true, lastWallpaperVisibility : false, userPresent : true hash: 37757111
[I][2025-04-02 +80 20:59:02.614][11862, 303][worker][daemon][ionia_event_][][WallpaperVisibilityObserver send MSG_RETURN_HOME
[I][2025-04-02 +80 20:59:02.614][11862, 303][worker][daemon][ionia_event_][][sendBroadcast : ionia.intent.action.RETURN_HOME
[I][2025-04-02 +80 20:59:02.614][11862, 303][worker][daemon][IoniaEvent][][onReceiveIoniaEvent: ionia.intent.action.RETURN_HOME, Bundle[{msg_from=1}]
[I][2025-04-02 +80 20:59:02.820][11862, 46][temporary-8][daemon][EventTransformInterceptor][][TransformAction: source = ionia.intent.action.RETURN_HOME, target = yyb.intent.action.PRESS_HOME
[I][2025-04-02 +80 20:59:02.824][11862, 46][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------onActionIntercept------yyb.intent.action.PRESS_HOME
[I][2025-04-02 +80 20:59:02.825][11862, 46][temporary-8][daemon][FLog_TouchSysInterceptor][][[temporary-8]TouchSysInterceptor:------updateConfig----dailyCount--6--frequencyControl--600
[W][2025-04-02 +80 20:59:02.826][11862, 46][temporary-8][daemon][FLog_TouchSysInterceptor][][TouchSysInterceptor:yyb.intent.action.PRESS_HOME ------inSilentTime---frequency----true, false
[W][2025-04-02 +80 20:59:03.260][11862, 39][temporary-3][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:59:03.262][11862, 39][temporary-3][daemon][HttpNetWorkTaskV2][][[293]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:293,useHttp2:false
[W][2025-04-02 +80 20:59:03.266][11862, 438][launch-438][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:59:03.267][11862, 438][launch-438][daemon][HttpNetWorkTaskV2][][[294]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:294,useHttp2:false
[W][2025-04-02 +80 20:59:03.267][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:59:03.270][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:59:03.284][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[294]callStart
[I][2025-04-02 +80 20:59:03.286][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[294]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:59:03.291][11862, 432][protocalManager-432][daemon][HttpNetWorkTaskV2][][[293]callStart
[I][2025-04-02 +80 20:59:03.291][11862, 432][protocalManager-432][daemon][HttpNetWorkTaskV2][][[293]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 20:59:03.299][11862, 432][protocalManager-432][daemon][CustomDns-mayybnew.3g.qq.com][][lookupImpl ret:[/**************, /**************]
[I][2025-04-02 +80 20:59:03.299][11862, 432][protocalManager-432][daemon][HttpNetWorkTaskV2][][[293]connectStart
[I][2025-04-02 +80 20:59:03.314][11862, 432][protocalManager-432][daemon][HttpNetWorkTaskV2][][[293]connectEnd protocol:http/1.1
[I][2025-04-02 +80 20:59:03.315][11862, 432][protocalManager-432][daemon][HttpNetWorkTaskV2][][[293]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:59:03.351][11862, 435][protocalManager-435][daemon][HttpNetWorkTaskV2][][[294]callEnd
[I][2025-04-02 +80 20:59:03.368][11862, 435][protocalManager-435][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:03.388][11862, 432][protocalManager-432][daemon][HttpNetWorkTaskV2][][[293]callEnd
[I][2025-04-02 +80 20:59:03.424][11862, 128][Binder:11862_6][daemon][Tinker.ProcessLifecycle][][App是进后台检查: activityCount=1,hasVisibleActivity=true
[I][2025-04-02 +80 20:59:03.432][11862, 432][protocalManager-432][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:03.446][11862, 128][Binder:11862_6][daemon][Tinker.ProcessLifecycle][][App是进后台检查: activityCount=0,hasVisibleActivity=false
[I][2025-04-02 +80 20:59:03.446][11862, 128][Binder:11862_6][daemon][Tinker.ProcessLifecycle][][【应用宝】进入后台:{"app_time":3245, "last_scene":2035}
[I][2025-04-02 +80 20:59:03.488][11862, 438][net_dedicated-438][daemon][QDFileUtils][][#deleteFile path:/storage/emulated/0/Android/data/com.tencent.android.qqdownloader/files/tassistant/push_manager.txt
[I][2025-04-02 +80 20:59:03.496][11862, 438][net_dedicated-438][daemon][QDFileUtils][][#deleteFile documentFile path:/storage/emulated/0/Android/data/com.tencent.android.qqdownloader/files/tassistant/push_manager.txt deleteResult:false
[I][2025-04-02 +80 20:59:03.496][11862, 438][net_dedicated-438][daemon][FileUtil][][#deleteFile: path=/storage/emulated/0/Android/data/com.tencent.android.qqdownloader/files/tassistant/push_manager.txt, deleteResult=false
[I][2025-04-02 +80 20:59:03.505][11862, 438][net_dedicated-438][daemon][QDFileUtils][][#deleteFile: end, fileExists=false
[I][2025-04-02 +80 20:59:03.505][11862, 438][net_dedicated-438][daemon][QDFileUtils][][#deleteFile: /storage/emulated/0/Android/data/com.tencent.android.qqdownloader/files/tassistant/push_manager.txt
[W][2025-04-02 +80 20:59:03.508][11862, 438][net_dedicated-438][daemon][wifi智能更新][][traceId:0 msg:拉取智能更新配置成功
[W][2025-04-02 +80 20:59:03.521][11862, 438][net_dedicated-438][daemon][wifi智能更新][][traceId:0 msg:可以下载的最大下载成功未安装个数为 ： 3
[W][2025-04-02 +80 20:59:03.521][11862, 438][net_dedicated-438][daemon][wifi智能更新][][traceId:0 msg:下载成功未安装的包可以保留 7 天
[W][2025-04-02 +80 20:59:03.524][11862, 438][net_dedicated-438][daemon][wifi智能更新][][traceId:0 msg:游戏悬浮窗的开关为 ： 1 最大的提示次数为 ： 2
[I][2025-04-02 +80 20:59:03.588][11862, 128][Binder:11862_6][daemon][LogTunnel][][processUAXXX 1
[W][2025-04-02 +80 20:59:03.589][11862, 128][Binder:11862_6][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:59:03.590][11862, 128][Binder:11862_6][daemon][HttpNetWorkTaskV2][][[295]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:295,useHttp2:false
[I][2025-04-02 +80 20:59:03.605][11862, 432][protocalManagerStatReport-432][daemon][HttpNetWorkTaskV2][][[295]callStart
[I][2025-04-02 +80 20:59:03.606][11862, 432][protocalManagerStatReport-432][daemon][HttpNetWorkTaskV2][][[295]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:59:03.636][11862, 438][net_dedicated-438][daemon][TrafficReminderThresholdUtils][][limitSize:200
[I][2025-04-02 +80 20:59:03.642][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:03.642][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:03.695][11862, 432][protocalManagerStatReport-432][daemon][HttpNetWorkTaskV2][][[295]callEnd
[I][2025-04-02 +80 20:59:03.715][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:03.715][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:03.738][11862, 432][protocalManagerStatReport-432][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:03.750][11862, 39][temporary-3][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 20:59:03.927][11862, 1*][main][daemon][UserStateReporter][][onAppStateChangeToMainThread: isFront = false, currentPageId = 2035, isAppFront = true
[I][2025-04-02 +80 20:59:03.927][11862, 1*][main][daemon][UserStateReporter][][onAppStateChange: isFront = false , currentPageId = 2035 , isAppFront = true
[I][2025-04-02 +80 20:59:03.927][11862, 1*][main][daemon][UserStateReporter][][onAppStateChange: app to background>>>> stayDurationMs = 3672 , currentPageId = 2035
[I][2025-04-02 +80 20:59:03.937][11862, 438][net_dedicated-438][daemon][wise_download][][traceId:0 msg:com.tencent.pangu.module.timer.job.AutoDownloadTimerJob start~~~
[I][2025-04-02 +80 20:59:03.944][11862, 1*][main][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:03.944][11862, 1*][main][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:03.947][11862, 1*][main][daemon][UserStateReporter][][reportUserStayDuration: success, durationMs = 3672 , scene = 2035
[I][2025-04-02 +80 20:59:03.947][11862, 1*][main][daemon][UserStateReporter][][stopHeartbeatReport: called.
[I][2025-04-02 +80 20:59:03.947][11862, 1*][main][daemon][UserStateReporter][][onAppStateChange: isAppFront = false , currentPageId = 2035
[I][2025-04-02 +80 20:59:03.947][11862, 1*][main][daemon][GlobalMonitor][][app go background, process:daemon
[E][2025-04-02 +80 20:59:03.988][11862, 438][net_dedicated-438][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:16 requestVersion:0 version:1743598742
[E][2025-04-02 +80 20:59:03.989][11862, 438][net_dedicated-438][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:17 requestVersion:5 version:5
[E][2025-04-02 +80 20:59:03.989][11862, 438][net_dedicated-438][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:1 requestVersion:1540383186 version:1540383186
[E][2025-04-02 +80 20:59:03.989][11862, 438][net_dedicated-438][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:18 requestVersion:2 version:2
[E][2025-04-02 +80 20:59:03.989][11862, 438][net_dedicated-438][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:2 requestVersion:1540383186 version:1540383186
[E][2025-04-02 +80 20:59:04.035][11862, 438][net_dedicated-438][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:19 requestVersion:1743596424 version:1743598742
[E][2025-04-02 +80 20:59:04.057][11862, 438][net_dedicated-438][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:20 requestVersion:1540383186 version:1540383186
[E][2025-04-02 +80 20:59:04.058][11862, 438][net_dedicated-438][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:4 requestVersion:1540383186 version:1540383186
[E][2025-04-02 +80 20:59:04.058][11862, 438][net_dedicated-438][daemon][CftGetNavigationEngine][][onRequestSuccessed parseNavigationResponse seq:-1 type:5 requestVersion:1743596424 version:1743598742
[I][2025-04-02 +80 20:59:04.122][11862, 42][temporary-4][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 20:59:04.123][11862, 42][temporary-4][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 20:59:04.149][11862, 42][temporary-4][daemon][LogTunnel][][processUAXXX 1
[W][2025-04-02 +80 20:59:04.196][11862, 220][LogProcessService-7][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 20:59:04.198][11862, 220][LogProcessService-7][daemon][HttpNetWorkTaskV2][][[296]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:296,useHttp2:false
[I][2025-04-02 +80 20:59:04.214][11862, 438][protocalManagerStatReport-438][daemon][HttpNetWorkTaskV2][][[296]callStart
[I][2025-04-02 +80 20:59:04.214][11862, 438][protocalManagerStatReport-438][daemon][HttpNetWorkTaskV2][][[296]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-02 +80 20:59:04.409][11862, 411][Binder:11862_E][daemon][ionia_event_WallpaperVisibilityObserver][][onWallpaperVisibilityRealChanged, visible : false, displayId : 0
[I][2025-04-02 +80 20:59:04.450][11862, 438][protocalManagerStatReport-438][daemon][HttpNetWorkTaskV2][][[296]callEnd
[I][2025-04-02 +80 20:59:04.465][11862, 438][protocalManagerStatReport-438][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-02 +80 20:59:04.603][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: frequency, skip. pollingFrequencySec = 600 , timeGapMs = 120411
[I][2025-04-02 +80 20:59:04.603][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: isPollingCheckEnable false
[I][2025-04-02 +80 20:59:04.607][11862, 35][temporary-1][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 20:59:04.613][11862, 43][temporary-5][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [10190秒]
[I][2025-04-02 +80 20:59:04.614][11862, 43][temporary-5][daemon][CmdMetrics][][all        13       69.23077     506      未探测          {-828=4}            
[I][2025-04-02 +80 20:59:04.614][11862, 43][temporary-5][daemon][CmdMetrics][][stat       6        100.0        673      未探测          {}                  
[I][2025-04-02 +80 20:59:04.615][11862, 43][temporary-5][daemon][CmdMetrics][][biz        7        42.857143    173      未探测          {-828=4}            
[E][2025-04-02 +80 20:59:04.643][11862, 39][temporary-3][daemon][CkCoreManager][][wrapper is not valid, bid:40, version:4, frequency:10800000, hasResource:true, checkTime:1743403384674
[I][2025-04-02 +80 20:59:04.650][11862, 303][worker][daemon][ionia_event_WallpaperVisibilityObserver][][onWallpaperVisibilityChanged, visible : false, lastWallpaperVisibility : true, userPresent : true hash: 74514116
[W][2025-04-02 +80 20:59:04.772][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 20:59:04.774][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 20:59:05.202][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: not freeze. diffMs = 39 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 10039 , taskStartElapsedRealtimeMs = 450374207 , reportEventTime = 450384246
[I][2025-04-02 +80 20:59:05.381][11862, 215][FloatingWindow-Refresh][daemon][ToolbarManager][][ToolbarManager >> toolbar data has ready. showQuickToolbar...
[I][2025-04-02 +80 20:59:05.407][11862, 215][FloatingWindow-Refresh][daemon][WildOptItemUpdater][][getOptRemoteViews optStyle=3
[I][2025-04-02 +80 20:59:05.408][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 1 , jumpUrl = tmast://found
[I][2025-04-02 +80 20:59:05.409][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 20:59:05.413][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0812fd
[I][2025-04-02 +80 20:59:05.417][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 2 , jumpUrl = tmast://optimizecheckpermission?SourceID=5193&ResourceID=45
[I][2025-04-02 +80 20:59:05.418][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 20:59:05.459][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0808d2
[I][2025-04-02 +80 20:59:05.462][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0808d9
[I][2025-04-02 +80 20:59:05.469][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][#updateItemForCommon: redDotId=2131232474, exposeText = 应用更新, redDotNum=13
[I][2025-04-02 +80 20:59:05.471][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0802ea
[I][2025-04-02 +80 20:59:05.480][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0806da
[I][2025-04-02 +80 20:59:05.482][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 3 , jumpUrl = tmast://update
[I][2025-04-02 +80 20:59:05.482][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 20:59:05.494][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][#updateItemForCommon: redDotId=2131232475, exposeText = 垃圾清理, redDotNum=0
[I][2025-04-02 +80 20:59:05.496][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0802eb
[I][2025-04-02 +80 20:59:05.499][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 4 , jumpUrl = tmast://spaceclean?back_jump_url=tmast%3A%2F%2Ffound
[I][2025-04-02 +80 20:59:05.499][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
