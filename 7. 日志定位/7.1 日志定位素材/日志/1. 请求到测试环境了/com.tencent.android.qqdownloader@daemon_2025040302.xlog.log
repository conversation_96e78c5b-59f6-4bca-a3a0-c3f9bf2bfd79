~~~~~ begin of mmap ~~~~~
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][GlobalMonitor][init GlobalMonitor
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][tag_LaunchSpeed][[main],[LaunchTagger][1]tag:Daemon_Application_onAttach
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][MMKVWrapper][MMKVWrapper fileName: /data/user/0/com.tencent.android.qqdownloader/files/mmkv/InterProcessKV, exists: true
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][MMKVInitiator][need prepare mmkv, from: M<PERSON>K<PERSON>Wrapper ,needForceFixPath: false ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][MMKVWrapper][MMKVWrapper init finish
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][Settings][NameValueCache: false
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][Settings][getString key:has_show_protocol:[true]
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][Settings][getString key:privacy_agree_mode:[2]
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][RightlySDKManager][setAllowPolicy:true, not init, ignore
[E][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][RDeliveryProvider_TAG][getConfigBoolean: RDelivery尚未初始化. key = enable_collect_imei , default = false
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][RightlySDKManager][initRightlySDK initConfig:InitConfig{debug=false, qdDeviceInfo=yyb8922819.wc.j@4335f1b, delayGetInstalledPackagesEnable=false, mmkvRootDirPath='/data/user/0/com.tencent.android.qqdownloader/files/mmkv', uid='', appVersion='8.9.2_8924130_2819', isDaemonProcess=true, collectImei=false}
[E][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][RightlySDKManager_PandoraEx.AutoCore][AutoStartMonitor Disable
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][RightlySDKManager][initSDK time =67
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][RightlySDKManager][getNetInfo, bssid = 02:00:00:00:00:00
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][RightlySDKManager][setAllowPolicy: true
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][initConfig: config = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk, false
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][get: key = key_zip_metadata_prefixchannelId, defValue = 0
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][get: key = channel_id, defValue = NA
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][ChannelIdManager][genChannelIdFromZipAndAssert: apkPath = /data/app/~~oTjgubmhivD3k-VcfVjVng==/com.tencent.android.qqdownloader-zT8LPO7hg9i5DBROjTNogg==/base.apk
[I][2025-04-03 +80 00:54:08.074][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelIdManager][return NA
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][report: {signType=V2}
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelIdManager][return NA
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelInfoManager][put: key = channel_id, value = NA
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelIdManager][genChannelIdFromZipAndAssert: mChannelId = NA
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][XLog][init, proccess:daemon
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelIdManager][getCurrentChannelId: genChannelIdFromAssert mCurrentChannelId = 0
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][ChannelIdManager][getCurrentChannelId: mCurrentChannelId = 0
[E][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][RDeliveryProvider_TAG][getConfigValue: RDelivery尚未初始化. key = privacy_info_report_config
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][BinderManager][connectToServiceOptimize
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][cache][daemon][DataBaseContextWrapper][[galtest] dbPath=/data/user/0/com.tencent.android.qqdownloader/files/database/mobile_plugin.db
[I][2025-04-03 +80 00:54:08.075][31992, 39][temporary-3][daemon][AstApp][][xlog init finished:daemon
[I][2025-04-03 +80 00:54:08.078][31992, 45][temporary-7][daemon][DataBaseContextWrapper][][[galtest] dbPath=/data/user/0/com.tencent.android.qqdownloader/files/database/mobile_ast_download2.db
[I][2025-04-03 +80 00:54:08.084][31992, 1*][main][daemon][tag_LaunchSpeed][][[main],[LaunchTagger][1]tag:Daemon_Application_onCreate_Begin
[I][2025-04-03 +80 00:54:08.092][31992, 1*][main][daemon][home_page_v9_load_fail][][processName = com.tencent.android.qqdownloader:daemon, importance = 230
[I][2025-04-03 +80 00:54:08.093][31992, 1*][main][daemon][home_page_v9_load_fail][][processName = com.tencent.android.qqdownloader:live, importance = 230
[I][2025-04-03 +80 00:54:08.093][31992, 1*][main][daemon][home_page_v9_load_fail][][other process exist: com.tencent.android.qqdownloader:live, ignore
[I][2025-04-03 +80 00:54:08.093][31992, 44][temporary-6][daemon][RdefenseInitTask][][init
[I][2025-04-03 +80 00:54:08.119][31992, 45][temporary-7][daemon][cscomm][][start load yyb_csech
[I][2025-04-03 +80 00:54:08.126][31992, 58][launch-58][daemon][BinderManager][][connectToServiceAsync
[I][2025-04-03 +80 00:54:08.127][31992, 1*][main][daemon][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[I][2025-04-03 +80 00:54:08.128][31992, 58][launch-58][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.129][31992, 58][launch-58][daemon][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&0_0_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-03 +80 00:54:08.134][31992, 25][Binder:31992_1][daemon][STGlobal][][setAppCaller(54, 1743612848128) from (6), from progress = daemon
java.lang.RuntimeException
	at yyb8922819.tb.xg.l(ProGuard:384)
	at yyb8922819.tb.xg.m(ProGuard:374)
	at com.tencent.assistant.db.contentprovider.SimpleDataTranslateProvider.d(ProGuard:74)
	at com.tencent.assistant.db.contentprovider.SimpleDataTranslateProvider.call(ProGuard:64)
	at android.content.ContentProvider.call(ContentProvider.java:2470)
	at android.content.ContentProvider$Transport.call(ContentProvider.java:515)
	at android.content.ContentProviderNative.onTransact(ContentProviderNative.java:295)
	at android.os.Binder.execTransactInternal(Binder.java:1192)
	at android.os.Binder.execTransact(Binder.java:1156)
[I][2025-04-03 +80 00:54:08.139][31992, 58][launch-58][daemon][FLog_login_log][][[launch-58]WxLoginEngine:wx load cache start
[I][2025-04-03 +80 00:54:08.140][31992, 39][temporary-3][daemon][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[E][2025-04-03 +80 00:54:08.141][31992, 25][Binder:31992_1][daemon][CallerSessionIdManager][][saveCallerSessionId: empty skip. caller = [54], sessionId = [] , java.lang.Exception
	at com.tencent.assistant.kplfrequency.xc.j(ProGuard:44)
	at yyb8922819.tb.xg.l(ProGuard:410)
	at yyb8922819.tb.xg.m(ProGuard:374)
	at com.tencent.assistant.db.contentprovider.SimpleDataTranslateProvider.d(ProGuard:74)
	at com.tencent.assistant.db.contentprovider.SimpleDataTranslateProvider.call(ProGuard:64)
	at android.content.ContentProvider.call(ContentProvider.java:2470)
	at android.content.ContentProvider$Transport.call(ContentProvider.java:515)
	at android.content.ContentProviderNative.onTransact(ContentProviderNative.java:295)
	at android.os.Binder.execTransactInternal(Binder.java:1192)
	at android.os.Binder.execTransact(Binder.java:1156)
[I][2025-04-03 +80 00:54:08.142][31992, 58][launch-58][daemon][RDeliveryProvider_TAG][][RDelivery lazy 初始化. guid = 2058663700530012608
[E][2025-04-03 +80 00:54:08.182][31992, 58][launch-58][daemon][YYBNetWorkEnvironment][][getProtocolReqHeadInfo: Global.traceId is invalid. start reset. needFix = true
[I][2025-04-03 +80 00:54:08.185][31992, 58][launch-58][daemon][YYBNetWorkEnvironment][][getProtocolReqHeadInfo: sessionId = 69df07cd-2ee9-4b76-9d3a-450c67aaf093
[I][2025-04-03 +80 00:54:08.185][31992, 66][io_thread][daemon][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 00:54:08.186][31992, 62][io_thread][daemon][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 00:54:08.188][31992, 69][io_thread][daemon][RDeliverySetting][][initUUID uuid = 781cab81-b1f7-4173-b11d-a7c8ae3cc59c
[I][2025-04-03 +80 00:54:08.188][31992, 1*][main][daemon][AliveFreezeCheckTask][][taskTimeIntervalsMs = [10000] , processFreezeCheckThreshold = 2.0 , thresholdConfig = 
[E][2025-04-03 +80 00:54:08.195][31992, 44][temporary-6][daemon][ActivityThreadHacker][][ApplicationThread$Stub fields is null
[I][2025-04-03 +80 00:54:08.200][31992, 43][temporary-5][daemon][LaunchSpeedSTManager][][traceId:0 msg:GlobalInit
[I][2025-04-03 +80 00:54:08.205][31992, 1*][main][daemon][ApplicationImpl][][[QDWebsocketServer] initForDeamon
[I][2025-04-03 +80 00:54:08.206][31992, 46][temporary-8][daemon][BinderManager][][connectToServiceOptimize
[E][2025-04-03 +80 00:54:08.207][31992, 44][temporary-6][daemon][Monitor][][Monitor init fail. key = service_monitor
[E][2025-04-03 +80 00:54:08.207][31992, 44][temporary-6][daemon][Monitor][][Monitor init fail. key = receiver_monitor
[E][2025-04-03 +80 00:54:08.207][31992, 44][temporary-6][daemon][Monitor][][Monitor init fail. key = activity_monitor
[E][2025-04-03 +80 00:54:08.207][31992, 44][temporary-6][daemon][Monitor][][Monitor init fail. key = process_monitor
[I][2025-04-03 +80 00:54:08.223][31992, 58][launch-58][daemon][SecretQ36Manager][][init
[I][2025-04-03 +80 00:54:08.227][31992, 58][launch-58][daemon][AutoDownloadUpdateEngine][][switchSupportMtFullDownload: sIsSupportMtFullDownload = true
[I][2025-04-03 +80 00:54:08.232][31992, 74][alive_report][daemon][AliveDurationTaskExecutor][][handleMessage: delay time is too short! taskId = 3 , runTimes = 1 , taskDelayTime = 0
[I][2025-04-03 +80 00:54:08.239][31992, 57][StartThread-57][daemon][DataBaseContextWrapper][][[galtest] dbPath=/data/user/0/com.tencent.android.qqdownloader/files/database/mobile_settings.db
[I][2025-04-03 +80 00:54:08.294][31992, 43][temporary-5][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.295][31992, 43][temporary-5][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.284][31992, 44][temporary-6][daemon][ActivityThreadHacker][][start Hook successful, dur = 112
[I][2025-04-03 +80 00:54:08.300][31992, 43][temporary-5][daemon][genQUA][][mQUA: TMAF_892_P_2819/082819&NA/082819/8924130_2819&12_31_2_2_0_2&139_67_14&HUAWEI_TASAN00_HUAWEI_TASAN00&NA&2819&V3
[I][2025-04-03 +80 00:54:08.300][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.303][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.<init>(ProGuard:133)
	at yyb8922819.e7.xc.m(ProGuard:144)
	at yyb8922819.e7.xc.l(ProGuard:137)
	at yyb8922819.e7.xe.getService(ProGuard:31)
	at yyb8922819.d9.xb.f(ProGuard:49)
	at com.tencent.assistant.os.OSPackageManager.isInstalledPackagesLoadReady(ProGuard:30)
	at yyb8922819.k8.xz.doInit(ProGuard:25)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.303][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.307][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.<init>(ProGuard:133)
	at yyb8922819.e7.xc.m(ProGuard:144)
	at yyb8922819.e7.xc.l(ProGuard:137)
	at yyb8922819.e7.xe.getService(ProGuard:31)
	at yyb8922819.d9.xb.f(ProGuard:49)
	at com.tencent.assistant.os.OSPackageManager.isInstalledPackagesLoadReady(ProGuard:30)
	at yyb8922819.k8.xz.doInit(ProGuard:25)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.307][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.317][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at yyb8922819.d9.xb.f(ProGuard:49)
	at com.tencent.assistant.os.OSPackageManager.isInstalledPackagesLoadReady(ProGuard:30)
	at yyb8922819.k8.xz.doInit(ProGuard:25)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.317][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.319][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at yyb8922819.d9.xb.f(ProGuard:49)
	at com.tencent.assistant.os.OSPackageManager.isInstalledPackagesLoadReady(ProGuard:30)
	at yyb8922819.k8.xz.doInit(ProGuard:25)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.322][31992, 57][StartThread-57][daemon][MainBinderManager][][queryBinder, binderCode : 1026 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.322][31992, 57][StartThread-57][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1026
[I][2025-04-03 +80 00:54:08.330][31992, 66][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 144, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 00:54:08.330][31992, 62][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 144, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 00:54:08.334][31992, 1*][main][daemon][FLog_qqlive_log][][[main]PluginLoadObserver:Don't observe other process like daemon、tools.

[I][2025-04-03 +80 00:54:08.334][31992, 1*][main][daemon][tag_LaunchSpeed][][[main],[LaunchTagger][1]tag:Daemon_Application_onCreate_End
[I][2025-04-03 +80 00:54:08.338][31992, 57][StartThread-57][daemon][DataBaseContextWrapper][][[galtest] dbPath=/data/user/0/com.tencent.android.qqdownloader/files/database/mobile_ast_daemon.db
[I][2025-04-03 +80 00:54:08.338][31992, 58][StartThread-58][daemon][FLog_StartInstrReport][][[StartThread-58]二次启动,
[I][2025-04-03 +80 00:54:08.340][31992, 58][StartThread-58][daemon][FLog_StartInstrReport][][[StartThread-58]发起广播,
[I][2025-04-03 +80 00:54:08.348][31992, 69][io_thread][daemon][RDelivery_DataManager_aa66b6582e_10001_][][loadDataFromDisk loadResult = true, cost = 159, dataMap.size = 322, memSize = 325.859375
[I][2025-04-03 +80 00:54:08.354][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.357][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at yyb8922819.l80.xb.j(ProGuard:114)
	at yyb8922819.l80.xb.c(ProGuard:50)
	at com.tencent.assistant.manager.SplashManager.D(ProGuard:345)
	at com.tencent.assistant.manager.SplashManager.F(ProGuard:413)
	at yyb8922819.k8.yx.doInit(ProGuard:25)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.357][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-03 +80 00:54:08.359][31992, 1*][main][daemon][BinderManager][][mBinderManagerConnection -> onServiceConnected
[I][2025-04-03 +80 00:54:08.359][31992, 1*][main][daemon][BinderManager][][mBinderManagerConnection -> onServiceConnected, mBinderManager = true
[I][2025-04-03 +80 00:54:08.361][31992, 47][SendEventDispatcher][daemon][BinderManager][][queryBinder, binderCode : 1, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 00:54:08.362][31992, 47][SendEventDispatcher][daemon][BinderManager][][addService, service : true
[E][2025-04-03 +80 00:54:08.365][31992, 57][StartThread-57][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at yyb8922819.l80.xb.j(ProGuard:114)
	at yyb8922819.l80.xb.c(ProGuard:50)
	at com.tencent.assistant.manager.SplashManager.D(ProGuard:345)
	at com.tencent.assistant.manager.SplashManager.F(ProGuard:413)
	at yyb8922819.k8.yx.doInit(ProGuard:25)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.365][31992, 57][StartThread-57][daemon][MainBinderManager][][queryBinder, binderCode : 1020 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.366][31992, 57][StartThread-57][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1020
[I][2025-04-03 +80 00:54:08.367][31992, 46][temporary-8][daemon][BinderManager][][queryBinder, binderCode : 4, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 00:54:08.367][31992, 46][temporary-8][daemon][BinderManager][][addService, service : true
[I][2025-04-03 +80 00:54:08.368][31992, 1*][main][daemon][FLog_login_log][][[main]LoginProxy:id = com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849, processId = 3,needRefresh = false, currProcess = daemon
[I][2025-04-03 +80 00:54:08.373][31992, 35][temporary-1][daemon][BinderManager][][queryBinder, binderCode : 2, mBinderManager = true, source : getService, binder:true
[I][2025-04-03 +80 00:54:08.373][31992, 57][StartThread-57][daemon][splashInfo][][不在起始时间范围内，抛弃 secondNow = 1743612848 SplashInfo{beginTime=1743523200, id=6119, title='4.2开放空间', desc='', endTime=1743609599, runTime=5, runTimes=1, hasRunTimes=1, imageUrl='https://cdn.yyb.gtimg.com/wupload/xy/yybcms/c1ESqw40.jpg', imagePath='/storage/emulated/0/Android/data/com.tencent.android.qqdownloader/files/tassistant/pic/A8FCC041DAE314717D0A6CFAC30538B81743596039091', imageDataDesc='dpi=1080*1995;time=2025-04-02 00:00:00~2025-04-02 23:59:59', status=1, splashBitmapDensity=1.0, btnImage='http://cms-40062.sh.gfp.tencent-cloud.com/xy/yybcms/1620-1080_1625627498186.png', btnPath='/storage/emulated/0/Android/data/com.tencent.android.qqdownloader/files/tassistant/pic/30D521B57B6439249235350EB10D444C', btnMarginLeft=10000, btnMarginBottom=200, btnHeight=77, btnWidth=287, splashType=1, targetType=4, target='tmast://appdetails?appid=54472008&pname=com.tencent.tmgp.Nekootan.kfkj.yhlm&appname=开放空间', recommend_id=[7, 3, 0, 94, 10, 0, 2, 29, 0, 12, 11, 26, 2, 0, 30, -65, 25, 16, -1, 45, 0, 12, 11, 42, 1, 11, -70, 22, 60, 123, 34, 83, 111, 117, 114, 99, 101, 84, 121, 112, 101, 34, 58, 34, 65, 80, 80, 34, 44, 34, 83, 111, 117, 114, 99, 101, 78, 97, 109, 101, 34, 58, 34, 83, 80, 76, 65, 83, 72, 34, 44, 34, 83, 111, 117, 114, 99, 101, 73, 68, 34, 58, 34, 54, 49, 49, 57, 34, 125, 38, 0, 61, 0, 12, 11, 61, 0, 12, 106, 0, 55, 2, 0, 1, 95, -93, 18, 103, -19, 42, 3, 41, 0, 1, 12, 57, 0, 1, 12, 72, 0, 3, 6, 4, 112, 99, 118, 114, 22, 1, 48, 6, 4, 101, 99, 112, 109, 22, 1, 48, 6, 5, 83, 99, 111, 114, 101, 22, 1, 48, 81, 6, -40, 108, 121, 12, 110, 0, 12, 12, 28, 38, 0, 60, 76, 86, 0, 102, 0, 118, 0, 127, 0, 89, 12, 22, 0, 38, 30, 80, 78, 71, 45, 115, 112, 108, 97, 115, 104, 65, 100, 97, 112, 116, 101, 114, 45, 50, 48, 50, 49, 48, 54, 49, 48, 45, 50, 55, 55, 60, 66, 103, -19, 42, 3, 89, 0, 2, 10, 0, 100, 22, 30, 80, 78, 71, 45, 115, 112, 108, 97, 115, 104, 65, 100, 97, 112, 116, 101, 114, 45, 50, 48, 50, 49, 48, 54, 49, 48, 45, 50, 55, 55, 44, 11, 10, 0, 2, 22, 1, 48, 44, 11, 107, 0, 45, 2, 103, -19, 42, 3, 18, 103, -19, 42, 3, 44, 54, 32, 54, 101, 48, 100, 48, 51, 54, 54, 52, 55, 55, 50, 54, 54, 57, 102, 50, 51, 57, 51, 55, 99, 98, 56, 100, 55, 54, 49, 52, 101, 56, 100], splashDataType=1, showCountMain=1, showCountSwitch=0, resumeHasRunTimes=0, h5Url='', isKingCard=0, priority=9, safetyZoneLeft=64, safetyZoneTop=280, tailorError=0, buttonText=点击跳转至详情页面, imageGausePath=/storage/emulated/0/Android/data/com.tencent.android.qqdownloader/files/tassistant/pic/A8FCC041DAE314717D0A6CFAC30538B81743596039091_gausePic, videoUrl=, shiplyResourceIds=8o2vnb0vstbvy46q, shiplyTaskId=0, buttonType=0}
[I][2025-04-03 +80 00:54:08.383][31992, 35][temporary-1][daemon][BinderManager][][addService, service : true
[I][2025-04-03 +80 00:54:08.399][31992, 35][temporary-1][daemon][SystemEventManager][][apn not changed:WIFI, from:BroadcastReceiver
[I][2025-04-03 +80 00:54:08.400][31992, 57][StartThread-57][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:54:08.400][31992, 57][StartThread-57][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:54:08.408][31992, 57][StartThread-57][daemon][EncryptQ36InitTask][][doInit
[I][2025-04-03 +80 00:54:08.409][31992, 57][StartThread-57][daemon][SecretQ36Manager][][refresh
[I][2025-04-03 +80 00:54:08.410][31992, 57][StartThread-57][daemon][SecretQ36Manager][][refresh requestFrequency: true
[I][2025-04-03 +80 00:54:08.410][31992, 42][temporary-4][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:54:08.410][31992, 42][temporary-4][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:54:08.411][31992, 57][StartThread-57][daemon][SecretQ36Manager][][refresh request
[I][2025-04-03 +80 00:54:08.414][31992, 35][temporary-1][daemon][SecretQ36Manager][][init do request new q36
[I][2025-04-03 +80 00:54:08.432][31992, 45][temporary-7][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:54:08.433][31992, 45][temporary-7][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:54:08.433][31992, 42][temporary-4][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:54:08.433][31992, 42][temporary-4][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:54:08.433][31992, 35][temporary-1][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:54:08.433][31992, 35][temporary-1][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:54:08.438][31992, 39][temporary-3][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:54:08.438][31992, 39][temporary-3][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:54:08.454][31992, 42][temporary-4][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:54:08.454][31992, 42][temporary-4][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:54:08.466][31992, 59][StartThread-59][daemon][MMKVInitiator][][need prepare mmkv, from: ResHubInitializer ,needForceFixPath: true ,currentPath: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 00:54:08.466][31992, 43][temporary-5][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.475][31992, 59][StartThread-59][daemon][MMKVInitiator][][MMKV already init in the correct dir: /data/user/0/com.tencent.android.qqdownloader/files/mmkv
[I][2025-04-03 +80 00:54:08.480][31992, 39][temporary-3][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:54:08.484][31992, 59][StartThread-59][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.485][31992, 59][StartThread-59][daemon][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[W][2025-04-03 +80 00:54:08.486][31992, 35][temporary-1][daemon][jimxia][][jimxia, get appCaller: 54 get appVia:
[I][2025-04-03 +80 00:54:08.503][31992, 59][StartThread-59][daemon][DataBaseContextWrapper][][[galtest] dbPath=/data/user/0/com.tencent.android.qqdownloader/files/database/mobile_plugin_new.db
[I][2025-04-03 +80 00:54:08.545][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.547][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.547][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.553][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.558][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.558][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.558][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.559][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.559][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-03 +80 00:54:08.571][31992, 35][temporary-1][daemon][HttpNetWorkTaskV2][][[2]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:2,useHttp2:false
[E][2025-04-03 +80 00:54:08.573][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.573][31992, 57][protocalManager-57][daemon][AbsNetWorkTask][][traceId:0 msg:The Request contains Auth, for cmdNames = Auth
[I][2025-04-03 +80 00:54:08.575][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.575][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.575][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.576][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.576][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[W][2025-04-03 +80 00:54:08.577][31992, 92][LogProcessService-1][daemon][jimxia][][jimxia, get appCaller: 54 get appVia:
[E][2025-04-03 +80 00:54:08.578][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.578][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.578][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.578][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-03 +80 00:54:08.579][31992, 45][temporary-7][daemon][LogTunnel][][processUAXXX 1
[E][2025-04-03 +80 00:54:08.580][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.580][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.581][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.581][31992, 42][temporary-4][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:54:08.581][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.581][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.581][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.582][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.582][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.583][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.583][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.583][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.583][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.584][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.584][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.585][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.585][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.585][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.585][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-03 +80 00:54:08.585][31992, 57][protocalManager-57][daemon][HttpNetWorkTaskV2][][traceId:0 msg:The Request contains Auth for reqeustId = 1 mRequestSeq = 2 cmdNames = Auth
[E][2025-04-03 +80 00:54:08.586][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.586][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.587][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.587][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.587][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.587][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.592][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.592][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.593][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.593][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.593][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.593][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.594][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.594][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.596][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.596][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.596][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.597][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.598][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.598][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-03 +80 00:54:08.598][31992, 57][protocalManager-57][daemon][HttpNetWorkTaskV2][][[2]callStart
[E][2025-04-03 +80 00:54:08.599][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.599][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.599][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.599][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-03 +80 00:54:08.599][31992, 57][protocalManager-57][daemon][HttpNetWorkTaskV2][][[2]dnsStart domainName:mayybnew.3g.qq.com
[E][2025-04-03 +80 00:54:08.601][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.601][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect process:daemon
[E][2025-04-03 +80 00:54:08.602][31992, 59][StartThread-59][daemon][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ef61e74 u0a3080 TRNB idle change:idle|uncached procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at com.tencent.assistant.utils.TemporaryThreadManager.startInTmpThreadIfNowInUIThread(ProGuard:126)
	at yyb8922819.e7.xc.g(ProGuard:255)
	at yyb8922819.e7.xc.n(ProGuard:225)
	at yyb8922819.e7.xe.getService(ProGuard:32)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:207)
	at com.tencent.assistant.plugin.GetPluginListEngine.pluginIsDiscard(ProGuard:197)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.refresh(ProGuard:169)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.<init>(ProGuard:125)
	at com.tencent.assistant.plugin.mgr.PluginInstalledManager.get(ProGuard:144)
	at com.tencent.assistant.plugin.PluginProxyUtils.getPlugin(ProGuard:1953)
	at com.tencent.pangu.reshub.ResHubInitializer.d(ProGuard:149)
	at com.tencent.pangu.reshub.ResHubInitializer.p(ProGuard:99)
	at yyb8922819.k8.ys.doInit(ProGuard:24)
	at com.tencent.assistant.module.init.AbstractInitTask.run(ProGuard:30)
	at yyb8922819.j8.xg$xb.run(ProGuard:50)
	at yyb8922819.tf0.xd.run(ProGuard:46)
	at yyb8922819.tf0.xe.run(ProGuard:26)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-03 +80 00:54:08.602][31992, 59][StartThread-59][daemon][MainBinderManager][][queryBinder, binderCode : 1013 binderManager:false binder:false
[E][2025-04-03 +80 00:54:08.602][31992, 59][StartThread-59][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1013
[I][2025-04-03 +80 00:54:08.605][31992, 57][protocalManager-57][daemon][CustomDnsTab][][nacDomains:[], domainsStr:
[I][2025-04-03 +80 00:54:08.605][31992, 57][protocalManager-57][daemon][CustomDnsTab][][httpDnsDomains:[], domainsStr:
[I][2025-04-03 +80 00:54:08.605][31992, 57][protocalManager-57][daemon][CustomDnsTab][][httpDnsRootDomains:[3g.qq.com, myapp.com], str:3g.qq.com,myapp.com
[I][2025-04-03 +80 00:54:08.606][31992, 59][StartThread-59][daemon][report_tag][][hookAms = false
[I][2025-04-03 +80 00:54:08.620][31992, 57][protocalManager-57][daemon][CustomDnsTab][][ipRotationEnable:true
[I][2025-04-03 +80 00:54:08.620][31992, 57][protocalManager-57][daemon][CustomDnsTab][][ipv6ProtectDomains:
[I][2025-04-03 +80 00:54:08.621][31992, 57][protocalManager-57][daemon][CustomDnsTab][][ipv6ProtectRootDomains:[]
[I][2025-04-03 +80 00:54:08.621][31992, 57][protocalManager-57][daemon][CustomDns-mayybnew.3g.qq.com][][lookupImpl ret:[/**************, /**************]
[I][2025-04-03 +80 00:54:08.622][31992, 57][protocalManager-57][daemon][HttpNetWorkTaskV2][][[2]connectStart
[I][2025-04-03 +80 00:54:08.623][31992, 57][protocalManager-57][daemon][CustomDnsTab][][wifiIpv6ProtectTime:0
[I][2025-04-03 +80 00:54:08.628][31992, 57][protocalManager-57][daemon][CustomDnsTab][][v4v6RaceEnable:false
[I][2025-04-03 +80 00:54:08.629][31992, 57][protocalManager-57][daemon][CustomDnsTab][][ipRaceEnable:false
[I][2025-04-03 +80 00:54:08.645][31992, 57][protocalManager-57][daemon][HttpNetWorkTaskV2][][[2]connectEnd protocol:http/1.1
[I][2025-04-03 +80 00:54:08.645][31992, 57][protocalManager-57][daemon][HttpNetWorkTaskV2][][[2]connectionAcquired connection:Connection{mayybnew.3g.qq.com:80, proxy=DIRECT hostAddress=/**************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-03 +80 00:54:08.666][31992, 59][StartThread-59][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.667][31992, 59][StartThread-59][daemon][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 00:54:08.668][31992, 59][StartThread-59][daemon][RDelivery_init_task][][doInit init RDelivery finish
[I][2025-04-03 +80 00:54:08.671][31992, 59][StartThread-59][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.671][31992, 59][StartThread-59][daemon][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 00:54:08.677][31992, 57][protocalManager-57][daemon][HttpNetWorkTaskV2][][[2]callEnd
[I][2025-04-03 +80 00:54:08.706][31992, 46][temporary-8][daemon][ProtocolReportUtilsNew][][succRatiosConfig:
        {"def_ratio":100, ",24,":500}
    
[I][2025-04-03 +80 00:54:08.707][31992, 57][protocalManager-57][daemon][SecretQ36Manager][][update qimei36: aW5jbHVkZXNwZWMyvm8iYKUIMLnqTmPIw5oitwN2clbdPlprX5903+9b2MkuQH6fK193jXDx/m4AVfeQnsFdriT8Cg3Yl9vja2m85w==
[I][2025-04-03 +80 00:54:08.708][31992, 57][protocalManager-57][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.709][31992, 57][protocalManager-57][daemon][HttpNetWorkTaskV2][][[1]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:1,useHttp2:false
[I][2025-04-03 +80 00:54:08.710][31992, 57][protocalManager-57][daemon][HttpNetWorkTaskV2][][[3]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:3,useHttp2:false
[I][2025-04-03 +80 00:54:08.710][31992, 42][temporary-4][daemon][SecretQ36Manager][][init receive new q36, old: 
[I][2025-04-03 +80 00:54:08.712][31992, 42][temporary-4][daemon][SecretQ36Manager][][init receive new q36, new: aW5jbHVkZXNwZWMyvm8iYKUIMLnqTmPIw5oitwN2clbdPlprX5903+9b2MkuQH6fK193jXDx/m4AVfeQnsFdriT8Cg3Yl9vja2m85w==
[I][2025-04-03 +80 00:54:08.712][31992, 42][temporary-4][daemon][SecretQ36Manager][][notifySecretChanged
[I][2025-04-03 +80 00:54:08.715][31992, 46][temporary-8][daemon][CmdMetrics][][metricsEnable:true
[I][2025-04-03 +80 00:54:08.717][31992, 46][temporary-8][daemon][CmdMetrics][][intervalNum:2|2
[I][2025-04-03 +80 00:54:08.721][31992, 59][protocalManagerStatReport-59][daemon][HttpNetWorkTaskV2][][[1]callStart
[I][2025-04-03 +80 00:54:08.721][31992, 59][protocalManagerStatReport-59][daemon][HttpNetWorkTaskV2][][[1]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-03 +80 00:54:08.725][31992, 58][protocalManagerStatReport-58][daemon][HttpNetWorkTaskV2][][[3]callStart
[I][2025-04-03 +80 00:54:08.726][31992, 58][protocalManagerStatReport-58][daemon][HttpNetWorkTaskV2][][[3]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-03 +80 00:54:08.740][31992, 59][protocalManagerStatReport-59][daemon][CustomDns-mayybstatnew.3g.qq.com][][lookupImpl ret:[/************, /*************]
[I][2025-04-03 +80 00:54:08.740][31992, 59][protocalManagerStatReport-59][daemon][HttpNetWorkTaskV2][][[1]connectStart
[I][2025-04-03 +80 00:54:08.741][31992, 58][protocalManagerStatReport-58][daemon][CustomDns-mayybstatnew.3g.qq.com][][lookupImpl ret:[/*************, /************]
[I][2025-04-03 +80 00:54:08.741][31992, 58][protocalManagerStatReport-58][daemon][HttpNetWorkTaskV2][][[3]connectStart
[I][2025-04-03 +80 00:54:08.755][31992, 59][protocalManagerStatReport-59][daemon][HttpNetWorkTaskV2][][[1]connectEnd protocol:http/1.1
[I][2025-04-03 +80 00:54:08.755][31992, 59][protocalManagerStatReport-59][daemon][HttpNetWorkTaskV2][][[1]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-03 +80 00:54:08.759][31992, 58][protocalManagerStatReport-58][daemon][HttpNetWorkTaskV2][][[3]connectEnd protocol:http/1.1
[I][2025-04-03 +80 00:54:08.759][31992, 58][protocalManagerStatReport-58][daemon][HttpNetWorkTaskV2][][[3]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/*************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-03 +80 00:54:08.785][31992, 59][protocalManagerStatReport-59][daemon][HttpNetWorkTaskV2][][[1]callEnd
[I][2025-04-03 +80 00:54:08.796][31992, 59][protocalManagerStatReport-59][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.797][31992, 58][protocalManagerStatReport-58][daemon][HttpNetWorkTaskV2][][[3]callEnd
[I][2025-04-03 +80 00:54:08.807][31992, 58][protocalManagerStatReport-58][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:08.814][31992, 57][net_dedicated-57][daemon][DataBaseContextWrapper][][[galtest] dbPath=/data/user/0/com.tencent.android.qqdownloader/files/database/st_report_daemon.db
[I][2025-04-03 +80 00:54:10.231][31992, 29][Binder:31992_3][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[W][2025-04-03 +80 00:54:11.581][31992, 92][LogProcessService-1][daemon][jimxia][][jimxia, get appCaller: 54 get appVia:
[I][2025-04-03 +80 00:54:11.587][31992, 92][LogProcessService-1][daemon][HttpNetWorkTaskV2][][[4]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:4,useHttp2:false
[I][2025-04-03 +80 00:54:11.614][31992, 57][protocalManagerStatReport-57][daemon][HttpNetWorkTaskV2][][[4]callStart
[I][2025-04-03 +80 00:54:11.619][31992, 57][protocalManagerStatReport-57][daemon][HttpNetWorkTaskV2][][[4]connectionAcquired connection:Connection{mayybstatnew.3g.qq.com:80, proxy=DIRECT hostAddress=/************:80 cipherSuite=none protocol=http/1.1}
[I][2025-04-03 +80 00:54:11.696][31992, 57][protocalManagerStatReport-57][daemon][HttpNetWorkTaskV2][][[4]callEnd
[I][2025-04-03 +80 00:54:11.755][31992, 57][protocalManagerStatReport-57][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:54:12.125][31992, 1*][main][daemon][FLog_login_log][][[main]LoginProxy:id = com.tencent.nucleus.socialcontact.login.WXIdentityInfo@182f7849, processId = 1,needRefresh = false, currProcess = daemon
[I][2025-04-03 +80 00:54:13.130][31992, 45][temporary-7][daemon][RdefenseIdleTask][][init
[I][2025-04-03 +80 00:54:13.265][31992, 76][DefaultHandler][daemon][ApplicationImpl][][[QDWebsocketServer] delay initTask run
[I][2025-04-03 +80 00:54:13.286][31992, 76][DefaultHandler][daemon][KeepAliveMainSwitchManager][][isDisableAllKeepAliveStrategy=false
[I][2025-04-03 +80 00:54:13.295][31992, 76][DefaultHandler][daemon][floatingwindow][][【启动悬浮窗服务】当前屏幕是亮状态，开始启动..
[I][2025-04-03 +80 00:54:13.301][31992, 76][DefaultHandler][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-03 +80 00:54:13.340][31992, 76][DefaultHandler][daemon][MainBinderManager][][queryBinder, binderCode : 1006 binderManager:false binder:false
[E][2025-04-03 +80 00:54:13.340][31992, 76][DefaultHandler][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1006
[I][2025-04-03 +80 00:54:13.340][31992, 76][DefaultHandler][daemon][MainBinderManager][][tryToConnect process:daemon
[I][2025-04-03 +80 00:54:13.352][31992, 76][DefaultHandler][daemon][MainBinderManager][][queryBinder, binderCode : 1006 binderManager:false binder:false
[E][2025-04-03 +80 00:54:13.353][31992, 76][DefaultHandler][daemon][FLog_MainBinderManager][][query code isn't exist,serverName:1006
[I][2025-04-03 +80 00:54:13.530][31992, 58][StartThread-58][daemon][rubbish][][<RubbishCacheTimerJob> startDailyTimer , period : 7200000, delay : 5000
[I][2025-04-03 +80 00:54:13.770][31992, 58][StartThread-58][daemon][OtherAppScanRuleManager][][#initRubbishRule
[I][2025-04-03 +80 00:54:13.965][31992, 58][StartThread-58][daemon][OtherAppCleanConfig][][#getConfig: configStr={"scanRuleUrl":"https://cms.myapp.com/xy/yybtech/HezFkBOE.json"}
[I][2025-04-03 +80 00:54:14.081][31992, 58][StartThread-58][daemon][OtherAppScanRuleManager][][#updateRubbishRule: 垃圾清理规则已存在，无需拉取. 路径：/data/user/0/com.tencent.android.qqdownloader/files/file/AB775D469291B30B5C187BDDE8EE4245
~~~~~ end of mmap ~~~~~[6455,6484][2025-04-03 +0800 02:14:15]
