~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 23:42:46.108][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][#updateItemForCommon: redDotId=2131232476, exposeText = 手机加速, redDotNum=0
[E][2025-04-02 +80 23:42:46.154][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: report freeze. diffMs = 9810952 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 9820952 , taskStartElapsedRealtimeMs = 450384247 , reportEventTime = 460205199
[I][2025-04-02 +80 23:42:46.155][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: duration = 9820952 , taskStartDate = 20250402 , currentDate = 20250402
[I][2025-04-02 +80 23:42:46.155][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: [20250402]-9820952 , durationMs = 9820952
[I][2025-04-02 +80 23:42:46.164][11862, 125][timer][daemon][WxTokenRefreshManager][][RefreshWxLoginToken --- Start check, skipExpirationCheck: false
[I][2025-04-02 +80 23:42:46.184][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.184][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:46.209][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0802ec
[I][2025-04-02 +80 23:42:46.212][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250402 , nowCount = 0 , lastExecuteTime = 1743598624192
[I][2025-04-02 +80 23:42:46.212][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-02 +80 23:42:46.219][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 5 , jumpUrl = tmast://optimizecheckpermission?SourceID=5193&ResourceID=45
[I][2025-04-02 +80 23:42:46.220][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:46.221][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 23:42:46.222][11862, 35][temporary-1][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[I][2025-04-02 +80 23:42:46.226][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.226][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:46.230][11862, 215][FloatingWindow-Refresh][daemon][ToolbarManager][][showQuickToolbar startForeground >> notificationId=127
[I][2025-04-02 +80 23:42:46.231][11862, 35][temporary-1][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[I][2025-04-02 +80 23:42:46.234][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.234][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:46.235][11862, 215][FloatingWindow-Refresh][daemon][ToolbarManager][][onTaskQueueDidFinished  errorCode=0
[W][2025-04-02 +80 23:42:46.239][11862, 125][timer][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 23:42:46.240][11862, 125][timer][daemon][HttpNetWorkTaskV2][][[297]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:297,useHttp2:false
[W][2025-04-02 +80 23:42:46.241][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-02 +80 23:42:46.241][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.241][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:46.242][11862, 125][timer][daemon][FLog_TouchSysInterceptor][][[timer]TouchSysInterceptor:-sys-config-sendRequest-
[I][2025-04-02 +80 23:42:46.243][11862, 125][timer][daemon][PreUpdateAppEngine][][sendRequest start
[W][2025-04-02 +80 23:42:46.243][11862, 125][timer][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 23:42:46.244][11862, 125][timer][daemon][HttpNetWorkTaskV2][][[298]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:298,useHttp2:false
[E][2025-04-02 +80 23:42:46.245][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 23:42:46.248][11862, 125][timer][daemon][GetTopVViewDynamicSplashInfoTimerJob][][GetTopViewSplashInfoTimerJob invoke, get DynamicSplash
[I][2025-04-02 +80 23:42:46.249][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.249][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:46.251][11862, 43][temporary-5][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-02 +80 23:42:46.251][11862, 43][temporary-5][daemon][ToolbarRequest][][sendRequest from:LOOP, process:daemon
[I][2025-04-02 +80 23:42:46.261][11862, 444][protocalManager-444][daemon][ProtocolPackage][][ dumpTicketInfo o4f6JuKk_ryMjoJT3fsC5sd_J8g0, 90_Epa2ssxfb_tN15D1FnJpugSBi6sVtgqpQb15AORG8MXe7--06rRD_LRwIrW6dDLxqTLJaELJ34VJGXUOQkmImzJfJLSHzFGVCmOKFbDtk8Q, 90_IXI_ZSzDNAFEDyH7Jo9c7342_pcNoSY1AQpZivSZ9lVLDh6Y79ODIY-odiyEwNyuZbaQRzq_m4Du-sKgYzv7557jBFC4n2HJiYk7fEDY3g0
[I][2025-04-02 +80 23:42:46.261][11862, 444][protocalManager-444][daemon][ProtocolPackage][][dumpTicketInfo packageRequestHead request UserInfo: 2
[I][2025-04-02 +80 23:42:46.279][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.279][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:46.280][11862, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=66
[I][2025-04-02 +80 23:42:46.290][11862, 444][protocalManager-444][daemon][HttpNetWorkTaskV2][][[297]callStart
[I][2025-04-02 +80 23:42:46.290][11862, 445][protocalManager-445][daemon][HttpNetWorkTaskV2][][[298]callStart
[I][2025-04-02 +80 23:42:46.291][11862, 445][protocalManager-445][daemon][HttpNetWorkTaskV2][][[298]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 23:42:46.291][11862, 444][protocalManager-444][daemon][HttpNetWorkTaskV2][][[297]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 23:42:46.302][11862, 445][protocalManager-445][daemon][HttpNetWorkTaskV2][][[298]callFailed
[E][2025-04-02 +80 23:42:46.302][11862, 445][protocalManager-445][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetPushAndPopupSystemCfg
[E][2025-04-02 +80 23:42:46.302][11862, 445][protocalManager-445][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetPushAndPopupSystemCfg
[I][2025-04-02 +80 23:42:46.302][11862, 444][protocalManager-444][daemon][HttpNetWorkTaskV2][][[297]callFailed
[E][2025-04-02 +80 23:42:46.303][11862, 444][protocalManager-444][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetUserInfo
[E][2025-04-02 +80 23:42:46.303][11862, 444][protocalManager-444][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetUserInfo
[I][2025-04-02 +80 23:42:46.326][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.326][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:46.331][11862, 215][FloatingWindow-Refresh][daemon][ToolbarManager][][ToolbarManager >> toolbar data has ready. showQuickToolbar...
[I][2025-04-02 +80 23:42:46.332][11862, 215][FloatingWindow-Refresh][daemon][WildOptItemUpdater][][getOptRemoteViews optStyle=3
[I][2025-04-02 +80 23:42:46.332][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 1 , jumpUrl = tmast://found
[I][2025-04-02 +80 23:42:46.333][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:46.334][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0812fd
[I][2025-04-02 +80 23:42:46.335][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 2 , jumpUrl = tmast://optimizecheckpermission?SourceID=5193&ResourceID=45
[I][2025-04-02 +80 23:42:46.336][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[W][2025-04-02 +80 23:42:46.344][11862, 104][Binder:11862_4][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 23:42:46.344][11862, 104][Binder:11862_4][daemon][HttpNetWorkTaskV2][][[299]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:299,useHttp2:false
[I][2025-04-02 +80 23:42:46.350][11862, 447][protocalManager-447][daemon][HttpNetWorkTaskV2][][[299]callStart
[I][2025-04-02 +80 23:42:46.350][11862, 447][protocalManager-447][daemon][HttpNetWorkTaskV2][][[299]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 23:42:46.350][11862, 447][protocalManager-447][daemon][HttpNetWorkTaskV2][][[299]callFailed
[E][2025-04-02 +80 23:42:46.350][11862, 447][protocalManager-447][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetAutoDownloadPatch
[E][2025-04-02 +80 23:42:46.351][11862, 447][protocalManager-447][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetAutoDownloadPatch
[I][2025-04-02 +80 23:42:46.352][11862, 42][temporary-4][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 23:42:46.358][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0808d2
[I][2025-04-02 +80 23:42:46.358][11862, 42][temporary-4][daemon][FLog_login_log][][[temporary-4]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-02 +80 23:42:46.359][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0808d9
[I][2025-04-02 +80 23:42:46.359][11862, 42][temporary-4][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 23:42:46.359][11862, 42][temporary-4][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [20012秒]
[I][2025-04-02 +80 23:42:46.359][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][#updateItemForCommon: redDotId=2131232474, exposeText = 应用更新, redDotNum=13
[I][2025-04-02 +80 23:42:46.359][11862, 42][temporary-4][daemon][CmdMetrics][][all        0        100.0        0        未探测          {}                  
[I][2025-04-02 +80 23:42:46.360][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0802ea
[W][2025-04-02 +80 23:42:46.378][11862, 46][temporary-8][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 23:42:46.401][11862, 35][temporary-1][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 23:42:46.405][11862, 104][Binder:11862_4][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-02 +80 23:42:46.415][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0806da
[I][2025-04-02 +80 23:42:46.419][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 3 , jumpUrl = tmast://update
[I][2025-04-02 +80 23:42:46.420][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:46.435][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][#updateItemForCommon: redDotId=2131232475, exposeText = 垃圾清理, redDotNum=0
[I][2025-04-02 +80 23:42:46.438][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0802eb
[I][2025-04-02 +80 23:42:46.440][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 4 , jumpUrl = tmast://spaceclean?back_jump_url=tmast%3A%2F%2Ffound
[I][2025-04-02 +80 23:42:46.441][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:46.445][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][#updateItemForCommon: redDotId=2131232476, exposeText = 手机加速, redDotNum=0
[I][2025-04-02 +80 23:42:46.447][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0802ec
[I][2025-04-02 +80 23:42:46.450][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 5 , jumpUrl = tmast://optimizecheckpermission?SourceID=5193&ResourceID=45
[I][2025-04-02 +80 23:42:46.451][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:46.457][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 23:42:46.459][11862, 215][FloatingWindow-Refresh][daemon][ToolbarManager][][showQuickToolbar startForeground >> notificationId=127
[I][2025-04-02 +80 23:42:46.468][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:46.468][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:46.470][11862, 215][FloatingWindow-Refresh][daemon][ToolbarManager][][onTaskQueueDidFinished  errorCode=0
[W][2025-04-02 +80 23:42:46.496][11862, 411][Binder:11862_E][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-02 +80 23:42:46.584][11862, 446][coroutine-bg-446][daemon][Profiler][][[DynamicSplashManagerDaemon] onEvent: >> TopViewRequest << #3 in segment None
Since None Start: 14,812,401,001,907 ns (20,012,005 ms);  Since Last: 0 ns (0 ms).
[I][2025-04-02 +80 23:42:46.714][11862, 43][temporary-5][daemon][rubbishSelectedSize][][getDeepScanSelectedCacheSize=0
[I][2025-04-02 +80 23:42:46.714][11862, 265][IntentService[WiseDownloadMessageQueue]][daemon][wise_download][][traceId:0 msg:com.tencent.assistant.receiver.WiseDownloadMonitor onHandleIntent() action=android.intent.action.SCREEN_OFF
[W][2025-04-02 +80 23:42:46.759][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 23:42:46.764][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 23:42:47.001][11862, 44][temporary-6][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:47.001][11862, 44][temporary-6][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:47.009][11862, 42][temporary-4][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:47.009][11862, 42][temporary-4][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:47.135][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:47.135][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:47.143][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:47.143][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:47.305][11862, 43][temporary-5][daemon][FLog_TouchSysInterceptor][][[temporary-5]TouchSysInterceptor:getDeskShowEvent funcId = 
[I][2025-04-02 +80 23:42:47.306][11862, 43][temporary-5][daemon][OptimizeManager][][使用新的打分规则统计
[I][2025-04-02 +80 23:42:47.306][11862, 43][temporary-5][daemon][NewPhoneOptimizeManager][][没有存储权限，分数为30
[I][2025-04-02 +80 23:42:47.306][11862, 43][temporary-5][daemon][OptimizeManager][][calcFinalScore finalScore = 30
[I][2025-04-02 +80 23:42:47.312][11862, 43][temporary-5][daemon][GetAppUpdateEntranceManager][][parseData: mTotalAppUpdateNum = 13, mAppSimpleDetailListSize = 13
[I][2025-04-02 +80 23:42:47.350][11862, 43][temporary-5][daemon][GetWildToolbarEngine][][#getRequestMap: requestMap = {displayPushList={}, busiTypeExposure=, remainSdcardSize=77193125888, rubbishSize=0, totalMem=7468, pendingInstallCount=0, bigFileSize=0, weComRubbishSize=0, freeMem=2390, videoSize=0, pendingInstallAppIDs=, wxSize=0, score=30, contentCreatedTime=, qqSize=0, baiduRubbishSize=0, storagePermission=false, dingTalkRubbishSize=0, updateSize=13, imageSize=0}
[I][2025-04-02 +80 23:42:47.378][11862, 43][temporary-5][daemon][ToolbarRequest][][doSendRequest, from:LOOP, process:daemon
[I][2025-04-02 +80 23:42:47.460][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 23:42:47.460][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 23:42:47.460][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 23:42:47.460][11862, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=9
[I][2025-04-02 +80 23:42:47.461][11862, 39][temporary-3][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 23:42:47.585][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. msg.what=UI_EVENT_UPDATE_MAIN_TAB_RED_DOT.
[I][2025-04-02 +80 23:42:47.585][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. showReddot=false. tabType=3
[I][2025-04-02 +80 23:42:47.585][11862, 1*][main][daemon][miles][][BadgeManager >> handleUIEvent. 要操作的是管理tab，并且是取消角标显示，则取消红点.
[I][2025-04-02 +80 23:42:47.585][11862, 1*][main][daemon][miles][][满足角标出现条件。lastBadgeNumber=0. number=0. passedHour=9
[I][2025-04-02 +80 23:42:47.588][11862, 39][temporary-3][daemon][badge_util][][applyCount num: 0, isHonor: false
[I][2025-04-02 +80 23:42:47.624][11862, 215][FloatingWindow-Refresh][daemon][ToolbarManager][][ToolbarManager >> toolbar data has ready. showQuickToolbar...
[I][2025-04-02 +80 23:42:47.626][11862, 215][FloatingWindow-Refresh][daemon][WildOptItemUpdater][][getOptRemoteViews optStyle=3
[I][2025-04-02 +80 23:42:47.626][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 1 , jumpUrl = tmast://found
[I][2025-04-02 +80 23:42:47.627][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:47.629][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0812fd
[I][2025-04-02 +80 23:42:47.630][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 2 , jumpUrl = tmast://optimizecheckpermission?SourceID=5193&ResourceID=45
[I][2025-04-02 +80 23:42:47.630][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:47.637][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0808d2
[I][2025-04-02 +80 23:42:47.639][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0808d9
[I][2025-04-02 +80 23:42:47.640][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][#updateItemForCommon: redDotId=2131232474, exposeText = 应用更新, redDotNum=13
[I][2025-04-02 +80 23:42:47.641][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0802ea
[I][2025-04-02 +80 23:42:47.642][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0806da
[I][2025-04-02 +80 23:42:47.643][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 3 , jumpUrl = tmast://update
[I][2025-04-02 +80 23:42:47.643][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:47.650][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][#updateItemForCommon: redDotId=2131232475, exposeText = 垃圾清理, redDotNum=0
[I][2025-04-02 +80 23:42:47.652][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0802eb
[I][2025-04-02 +80 23:42:47.653][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 4 , jumpUrl = tmast://spaceclean?back_jump_url=tmast%3A%2F%2Ffound
[I][2025-04-02 +80 23:42:47.653][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:47.654][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][#updateItemForCommon: redDotId=2131232476, exposeText = 手机加速, redDotNum=0
[I][2025-04-02 +80 23:42:47.655][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][[StatusBarUtil] setRemoteTextViewColor textViewId=7f0802ec
[I][2025-04-02 +80 23:42:47.656][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarDecider][][createClickIntent: index = 5 , jumpUrl = tmast://optimizecheckpermission?SourceID=5193&ResourceID=45
[I][2025-04-02 +80 23:42:47.656][11862, 215][FloatingWindow-Refresh][daemon][AbstractNotificationService][][fixV2: return true.
[I][2025-04-02 +80 23:42:47.658][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-02 +80 23:42:47.659][11862, 215][FloatingWindow-Refresh][daemon][ToolbarManager][][showQuickToolbar startForeground >> notificationId=127
[I][2025-04-02 +80 23:42:47.665][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:47.665][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-02 +80 23:42:47.666][11862, 215][FloatingWindow-Refresh][daemon][ToolbarManager][][onTaskQueueDidFinished  errorCode=0
[I][2025-04-02 +80 23:42:47.671][11862, 39][temporary-3][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-02 +80 23:42:47.671][11862, 39][temporary-3][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[W][2025-04-02 +80 23:42:48.293][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 23:42:48.294][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-02 +80 23:42:48.305][11862, 446][protocalManager-446][daemon][ProtocolPackage][][ dumpTicketInfo o4f6JuKk_ryMjoJT3fsC5sd_J8g0, 90_Epa2ssxfb_tN15D1FnJpugSBi6sVtgqpQb15AORG8MXe7--06rRD_LRwIrW6dDLxqTLJaELJ34VJGXUOQkmImzJfJLSHzFGVCmOKFbDtk8Q, 90_IXI_ZSzDNAFEDyH7Jo9c7342_pcNoSY1AQpZivSZ9lVLDh6Y79ODIY-odiyEwNyuZbaQRzq_m4Du-sKgYzv7557jBFC4n2HJiYk7fEDY3g0
[I][2025-04-02 +80 23:42:48.305][11862, 446][protocalManager-446][daemon][ProtocolPackage][][dumpTicketInfo packageRequestHead request UserInfo: 2
[I][2025-04-02 +80 23:42:48.308][11862, 452][protocalManager-452][daemon][HttpNetWorkTaskV2][][[298]callStart
[I][2025-04-02 +80 23:42:48.308][11862, 452][protocalManager-452][daemon][HttpNetWorkTaskV2][][[298]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-02 +80 23:42:48.311][11862, 446][protocalManager-446][daemon][HttpNetWorkTaskV2][][[297]callStart
[I][2025-04-02 +80 23:42:48.311][11862, 452][protocalManager-452][daemon][HttpNetWorkTaskV2][][[298]callFailed
[I][2025-04-02 +80 23:42:48.311][11862, 446][protocalManager-446][daemon][HttpNetWorkTaskV2][][[297]dnsStart domainName:mayybnew.3g.qq.com
[E][2025-04-02 +80 23:42:48.312][11862, 452][protocalManager-452][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetPushAndPopupSystemCfg
[E][2025-04-02 +80 23:42:48.312][11862, 452][protocalManager-452][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetPushAndPopupSystemCfg
[I][2025-04-02 +80 23:42:48.312][11862, 446][protocalManager-446][daemon][HttpNetWorkTaskV2][][[297]callFailed
[E][2025-04-02 +80 23:42:48.312][11862, 446][protocalManager-446][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetUserInfo
[E][2025-04-02 +80 23:42:48.312][11862, 446][protocalManager-446][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetUserInfo
[W][2025-04-02 +80 23:42:49.755][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-02 +80 23:42:49.756][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[E][2025-04-03 +80 00:32:41.119][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: report freeze. diffMs = 2984912 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 2994912 , taskStartElapsedRealtimeMs = 460205252 , reportEventTime = 463200164
[I][2025-04-03 +80 00:32:41.141][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: duration = 2994912 , taskStartDate = 20250402 , currentDate = 20250403
[I][2025-04-03 +80 00:32:41.141][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: [20250402]-1035235 , [20250403]-1959677 , durationMs = 2994912
[I][2025-04-03 +80 00:32:41.173][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.173][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.174][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: [20250403]-1959677 , durationMs = 2994912
[I][2025-04-03 +80 00:32:41.185][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.185][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.187][11862, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=66
[I][2025-04-03 +80 00:32:41.188][11862, 411][Binder:11862_E][daemon][HttpNetWorkTaskV2][][[301]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:301,useHttp2:false
[W][2025-04-03 +80 00:32:41.193][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[I][2025-04-03 +80 00:32:41.200][11862, 453][protocalManager-453][daemon][HttpNetWorkTaskV2][][[301]callStart
[I][2025-04-03 +80 00:32:41.200][11862, 453][protocalManager-453][daemon][HttpNetWorkTaskV2][][[301]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.201][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.201][11862, 75][alive_report][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.204][11862, 1*][main][daemon][OptimizeSubScore][][isCharging=false;Battery Level=66
[I][2025-04-03 +80 00:32:41.205][11862, 453][protocalManager-453][daemon][HttpNetWorkTaskV2][][[301]callFailed
[E][2025-04-03 +80 00:32:41.205][11862, 453][protocalManager-453][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = CheckSelfUpdate
[E][2025-04-03 +80 00:32:41.206][11862, 453][protocalManager-453][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = CheckSelfUpdate
[I][2025-04-03 +80 00:32:41.215][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.221][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.226][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 1 图片无变化，不刷新
[I][2025-04-03 +80 00:32:41.226][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 2 内容无变化，不刷新
[I][2025-04-03 +80 00:32:41.226][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 3 内容无变化，不刷新
[I][2025-04-03 +80 00:32:41.227][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 4 内容无变化，不刷新
[I][2025-04-03 +80 00:32:41.227][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 5 内容无变化，不刷新
[I][2025-04-03 +80 00:32:41.227][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][item 6 内容无变化，不刷新
[I][2025-04-03 +80 00:32:41.259][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------onActionIntercept------yyb.intent.action.PROCESS_ALIVE_LOOPER
[I][2025-04-03 +80 00:32:41.261][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateConfig----dailyCount--6--frequencyControl--600
[I][2025-04-03 +80 00:32:41.262][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions------2016
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 14
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 15
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 16
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 17
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 24
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 19
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---0, 25
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 37
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 36
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 34
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 28
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 30
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 29
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---item---1, 32
[I][2025-04-03 +80 00:32:41.263][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:------updateActions---end---14,[, , , , , , , , , , , , , ]
[I][2025-04-03 +80 00:32:41.263][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.264][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.291][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:enableSkipLauncherCondition: configEnable = true, isExposureInTwoDay = false
[W][2025-04-03 +80 00:32:41.299][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][TouchSysInterceptor:#checkGlobalConditionStatus: >> 请求前check 场景条件不通过:SceneCondition{class=ScreenOnCondition, type=2030000, feature=1, errorCode=0, extraData=null}
[I][2025-04-03 +80 00:32:41.319][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.319][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.321][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=14, reachType=1
[I][2025-04-03 +80 00:32:41.335][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=15, reachType=1
[I][2025-04-03 +80 00:32:41.337][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.338][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.355][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.356][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.364][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=16, reachType=1
[I][2025-04-03 +80 00:32:41.367][11862, 46][temporary-8][daemon][HttpNetWorkTaskV2][][[300]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:300,useHttp2:false
[I][2025-04-03 +80 00:32:41.379][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=17, reachType=1
[I][2025-04-03 +80 00:32:41.387][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.387][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.393][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=24, reachType=1
[I][2025-04-03 +80 00:32:41.396][11862, 451][protocalManager-451][daemon][HttpNetWorkTaskV2][][[300]callStart
[I][2025-04-03 +80 00:32:41.396][11862, 451][protocalManager-451][daemon][HttpNetWorkTaskV2][][[300]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.399][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.399][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.401][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=19, reachType=1
[I][2025-04-03 +80 00:32:41.405][11862, 451][protocalManager-451][daemon][HttpNetWorkTaskV2][][[300]callFailed
[I][2025-04-03 +80 00:32:41.405][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.405][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[E][2025-04-03 +80 00:32:41.406][11862, 451][protocalManager-451][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetFlashTopView
[E][2025-04-03 +80 00:32:41.406][11862, 451][protocalManager-451][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetFlashTopView
[I][2025-04-03 +80 00:32:41.407][11862, 46][temporary-8][daemon][TopViewDynamicSplashEngine][][sendRequest, seq: 255
[W][2025-04-03 +80 00:32:41.407][11862, 104][Binder:11862_4][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.408][11862, 104][Binder:11862_4][daemon][HttpNetWorkTaskV2][][[302]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:302,useHttp2:false
[I][2025-04-03 +80 00:32:41.412][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.412][11862, 215][FloatingWindow-Refresh][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.412][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=25, reachType=0
[I][2025-04-03 +80 00:32:41.413][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:--getMsgInfo---OptPush
[I][2025-04-03 +80 00:32:41.414][11862, 42][temporary-4][daemon][assistant][][resetAllAnotherDay
[I][2025-04-03 +80 00:32:41.414][11862, 215][FloatingWindow-Refresh][daemon][miles][][FloatingWindowService >> 上报今天工具栏开启量。当前日期：20250403
[W][2025-04-03 +80 00:32:41.416][11862, 37][temporary-2][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.420][11862, 215][FloatingWindow-Refresh][daemon][miles][][上报今天权限开启情况。当前日期：20250403. type:0. 开启情况：false
[I][2025-04-03 +80 00:32:41.421][11862, 37][temporary-2][daemon][HttpNetWorkTaskV2][][[303]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:303,useHttp2:false
[I][2025-04-03 +80 00:32:41.422][11862, 37][temporary-2][daemon][FLog_login_log][][[temporary-2]TimeChangeReceiver:时间变化 action android.intent.action.TIME_TICK
[I][2025-04-03 +80 00:32:41.426][11862, 215][FloatingWindow-Refresh][daemon][miles][][上报今天权限开启情况。当前日期：20250403. type:1. 开启情况：false
[I][2025-04-03 +80 00:32:41.428][11862, 457][protocalManagerStatReport-457][daemon][HttpNetWorkTaskV2][][[302]callStart
[I][2025-04-03 +80 00:32:41.428][11862, 457][protocalManagerStatReport-457][daemon][HttpNetWorkTaskV2][][[302]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.428][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:toolBarExposure:0,15,6,1,4
[I][2025-04-03 +80 00:32:41.428][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:--content--empty--just--return--
[I][2025-04-03 +80 00:32:41.428][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=37, reachType=1
[W][2025-04-03 +80 00:32:41.433][11862, 43][temporary-5][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.434][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=36, reachType=1
[I][2025-04-03 +80 00:32:41.434][11862, 43][temporary-5][daemon][HttpNetWorkTaskV2][][[304]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:304,useHttp2:false
[W][2025-04-03 +80 00:32:41.436][11862, 39][temporary-3][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.437][11862, 215][FloatingWindow-Refresh][daemon][miles][][上报今天权限开启情况。当前日期：20250403. type:2. 开启情况：false
[I][2025-04-03 +80 00:32:41.438][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=34, reachType=1
[I][2025-04-03 +80 00:32:41.441][11862, 39][temporary-3][daemon][HttpNetWorkTaskV2][][[305]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:305,useHttp2:false
[I][2025-04-03 +80 00:32:41.444][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=28, reachType=1
[I][2025-04-03 +80 00:32:41.445][11862, 458][protocalManager-458][daemon][HttpNetWorkTaskV2][][[303]callStart
[I][2025-04-03 +80 00:32:41.445][11862, 458][protocalManager-458][daemon][HttpNetWorkTaskV2][][[303]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.447][11862, 458][protocalManager-458][daemon][HttpNetWorkTaskV2][][[303]callFailed
[E][2025-04-03 +80 00:32:41.447][11862, 458][protocalManager-458][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetMsgPushList
[E][2025-04-03 +80 00:32:41.447][11862, 458][protocalManager-458][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetMsgPushList
[I][2025-04-03 +80 00:32:41.448][11862, 215][FloatingWindow-Refresh][daemon][miles][][上报今天权限开启情况。当前日期：20250403. type:3. 开启情况：true
[I][2025-04-03 +80 00:32:41.449][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=30, reachType=1
[I][2025-04-03 +80 00:32:41.452][11862, 215][FloatingWindow-Refresh][daemon][miles][][FloatingWindowService >> 上报今天角标状态。当前日期：20250403
[I][2025-04-03 +80 00:32:41.457][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=29, reachType=1
[I][2025-04-03 +80 00:32:41.462][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:#ignoreBySwitch: busiType=32, reachType=1
[I][2025-04-03 +80 00:32:41.474][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.474][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.480][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:IManager-run--getList,size:0
[I][2025-04-03 +80 00:32:41.480][11862, 75][alive_report][daemon][PreUpdateAppEngine][][PersonalizedMessageDataManager#sendRequest eventCode=2016
[I][2025-04-03 +80 00:32:41.480][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][[alive_report]TouchSysInterceptor:PersonalizedMessageDataManager#sendRequest eventCode=2016
[E][2025-04-03 +80 00:32:41.480][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][TouchSysInterceptor:PersonalizedRequestPreHandler: filterUnimportantMessage, reqList is null or empty, return
[W][2025-04-03 +80 00:32:41.480][11862, 75][alive_report][daemon][FLog_TouchSysInterceptor][][TouchSysInterceptor:PersonalizedMessageDataManager#sendRequest: reqList is empty
[I][2025-04-03 +80 00:32:41.480][11862, 75][alive_report][daemon][PreUpdateAppEngine][][PersonalizedMessageDataManager#sendRequest: reqList is empty 2016
[I][2025-04-03 +80 00:32:41.481][11862, 75][alive_report][daemon][TouchSysInterceptor][][send looper event
[I][2025-04-03 +80 00:32:41.482][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][isPollingCheckEnable: true, formatMsToDay = 20250403 , nowCount = 0 , lastExecuteTime = 1743608566222
[I][2025-04-03 +80 00:32:41.482][11862, 75][alive_report][daemon][MtAppCheckTask-YYBMicroTerminal][][pollingCheckMtAppState: start checkAndPullApp, switch on
[I][2025-04-03 +80 00:32:41.485][11862, 42][temporary-4][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.486][11862, 336][Binder:11862_A][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.492][11862, 44][temporary-6][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.498][11862, 35][temporary-1][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.502][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.503][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.512][11862, 44][temporary-6][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.512][11862, 44][temporary-6][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.516][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.517][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.524][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.524][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[W][2025-04-03 +80 00:32:41.527][11862, 35][temporary-1][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.533][11862, 460][protocalManager-460][daemon][HttpNetWorkTaskV2][][[305]callStart
[I][2025-04-03 +80 00:32:41.534][11862, 460][protocalManager-460][daemon][HttpNetWorkTaskV2][][[305]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.534][11862, 35][temporary-1][daemon][HttpNetWorkTaskV2][][[306]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:306,useHttp2:false
[I][2025-04-03 +80 00:32:41.535][11862, 460][protocalManager-460][daemon][HttpNetWorkTaskV2][][[305]callFailed
[E][2025-04-03 +80 00:32:41.536][11862, 460][protocalManager-460][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetSetting
[E][2025-04-03 +80 00:32:41.536][11862, 460][protocalManager-460][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetSetting
[I][2025-04-03 +80 00:32:41.537][11862, 44][temporary-6][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.537][11862, 44][temporary-6][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[W][2025-04-03 +80 00:32:41.541][11862, 462][launch-462][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.541][11862, 45][temporary-7][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.545][11862, 462][launch-462][daemon][HttpNetWorkTaskV2][][[307]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:307,useHttp2:false
[I][2025-04-03 +80 00:32:41.547][11862, 459][protocalManager-459][daemon][HttpNetWorkTaskV2][][[304]callStart
[I][2025-04-03 +80 00:32:41.548][11862, 459][protocalManager-459][daemon][HttpNetWorkTaskV2][][[304]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.548][11862, 464][net_dedicated-464][daemon][FLog_login_log][][[net_dedicated-464]Donaldxu-GetUserInfo:onRequestFailed errorCode -828
[I][2025-04-03 +80 00:32:41.549][11862, 459][protocalManager-459][daemon][HttpNetWorkTaskV2][][[304]callFailed
[E][2025-04-03 +80 00:32:41.549][11862, 459][protocalManager-459][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetToolBar
[E][2025-04-03 +80 00:32:41.550][11862, 459][protocalManager-459][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetToolBar
[I][2025-04-03 +80 00:32:41.556][11862, 446][protocalManager-446][daemon][HttpNetWorkTaskV2][][[307]callStart
[I][2025-04-03 +80 00:32:41.557][11862, 446][protocalManager-446][daemon][HttpNetWorkTaskV2][][[307]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.558][11862, 446][protocalManager-446][daemon][HttpNetWorkTaskV2][][[307]callFailed
[E][2025-04-03 +80 00:32:41.559][11862, 446][protocalManager-446][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = InitYyb
[E][2025-04-03 +80 00:32:41.559][11862, 446][protocalManager-446][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = InitYyb
[I][2025-04-03 +80 00:32:41.561][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[I][2025-04-03 +80 00:32:41.562][11862, 44][temporary-6][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.562][11862, 44][temporary-6][daemon][MtAppCheckTask-YYBMicroTerminal][][checkAndPullApp: start init, pkgName = all_mt
[E][2025-04-03 +80 00:32:41.564][11862, 37][temporary-2][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[W][2025-04-03 +80 00:32:41.565][11862, 464][net_dedicated-464][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.566][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.566][11862, 46][temporary-8][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.567][11862, 464][net_dedicated-464][daemon][HttpNetWorkTaskV2][][[308]HttpNetWorkTaskV2 postUrl:http://mayybnew.3g.qq.com:80,seq:308,useHttp2:false
[I][2025-04-03 +80 00:32:41.569][11862, 37][temporary-2][daemon][TimerCleanManager][][storage permission not granted
[I][2025-04-03 +80 00:32:41.569][11862, 452][protocalManager-452][daemon][ProtocolPackage][][ dumpTicketInfo o4f6JuKk_ryMjoJT3fsC5sd_J8g0, 90_Epa2ssxfb_tN15D1FnJpugSBi6sVtgqpQb15AORG8MXe7--06rRD_LRwIrW6dDLxqTLJaELJ34VJGXUOQkmImzJfJLSHzFGVCmOKFbDtk8Q, 90_IXI_ZSzDNAFEDyH7Jo9c7342_pcNoSY1AQpZivSZ9lVLDh6Y79ODIY-odiyEwNyuZbaQRzq_m4Du-sKgYzv7557jBFC4n2HJiYk7fEDY3g0
[I][2025-04-03 +80 00:32:41.569][11862, 452][protocalManager-452][daemon][ProtocolPackage][][dumpTicketInfo packageRequestHead request UserInfo: 2
[I][2025-04-03 +80 00:32:41.570][11862, 37][temporary-2][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.574][11862, 39][temporary-3][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[I][2025-04-03 +80 00:32:41.575][11862, 452][protocalManager-452][daemon][HttpNetWorkTaskV2][][[308]callStart
[I][2025-04-03 +80 00:32:41.576][11862, 452][protocalManager-452][daemon][HttpNetWorkTaskV2][][[308]dnsStart domainName:mayybnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.577][11862, 452][protocalManager-452][daemon][HttpNetWorkTaskV2][][[308]callFailed
[E][2025-04-03 +80 00:32:41.577][11862, 452][protocalManager-452][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = GetUserInfo
[E][2025-04-03 +80 00:32:41.578][11862, 452][protocalManager-452][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = GetUserInfo
[I][2025-04-03 +80 00:32:41.581][11862, 37][temporary-2][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.581][11862, 43][temporary-5][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.581][11862, 43][temporary-5][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[W][2025-04-03 +80 00:32:41.582][11862, 37][temporary-2][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.583][11862, 37][temporary-2][daemon][HttpNetWorkTaskV2][][[309]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:309,useHttp2:false
[I][2025-04-03 +80 00:32:41.584][11862, 37][temporary-2][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.584][11862, 37][temporary-2][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.584][11862, 37][temporary-2][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.585][11862, 37][temporary-2][daemon][CmdMetrics][][type----   count---- rate------   cost----- weak-----    error-----          [23007秒]
[I][2025-04-03 +80 00:32:41.585][11862, 46][temporary-8][daemon][LogTunnel][][processUAXXX 1
[W][2025-04-03 +80 00:32:41.586][11862, 46][temporary-8][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.587][11862, 46][temporary-8][daemon][HttpNetWorkTaskV2][][[310]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:310,useHttp2:false
[I][2025-04-03 +80 00:32:41.588][11862, 44][temporary-6][daemon][MtAppCheckTask-YYBMicroTerminal][][needToPlMtApp is empty. pkgName = all_mt
[E][2025-04-03 +80 00:32:41.590][11862, 39][temporary-3][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.597][11862, 37][temporary-2][daemon][CmdMetrics][][all        2        0.0          0        未探测          {-828=2}            
[I][2025-04-03 +80 00:32:41.598][11862, 45][temporary-7][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.598][11862, 45][temporary-7][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.598][11862, 37][temporary-2][daemon][CmdMetrics][][biz        2        0.0          0        未探测          {-828=2}            
[I][2025-04-03 +80 00:32:41.600][11862, 37][temporary-2][daemon][ChannelIdManager][][getChannelId: mChannelId = NA
[I][2025-04-03 +80 00:32:41.600][11862, 37][temporary-2][daemon][ChannelIdManager][][getCurrentChannelId: mCurrentChannelId = 0
[I][2025-04-03 +80 00:32:41.604][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] android data bypass check result:false
[E][2025-04-03 +80 00:32:41.604][11862, 35][temporary-1][daemon][QDFileUtils][][[galtest] no perm, check result may be not right, no cache
[I][2025-04-03 +80 00:32:41.606][11862, 447][protocalManagerStatReport-447][daemon][HttpNetWorkTaskV2][][[309]callStart
[I][2025-04-03 +80 00:32:41.606][11862, 461][NetProberSDK][daemon][NetProbe/ApMonitor][][updateApn:updateApn from init,result:NET_TYPE_UNKNOWN|sig:-1|
[I][2025-04-03 +80 00:32:41.606][11862, 447][protocalManagerStatReport-447][daemon][HttpNetWorkTaskV2][][[309]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.610][11862, 463][protocalManagerStatReport-463][daemon][HttpNetWorkTaskV2][][[306]callStart
[I][2025-04-03 +80 00:32:41.610][11862, 463][protocalManagerStatReport-463][daemon][HttpNetWorkTaskV2][][[306]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.618][11862, 461][NetProberSDK][daemon][NetProbe/ApMonitor][][updateApn:updateApn from CONNECTIVITY_CHANGE,result:NET_TYPE_UNKNOWN|sig:-1|
[I][2025-04-03 +80 00:32:41.619][11862, 44][temporary-6][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.620][11862, 39][temporary-3][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.620][11862, 42][temporary-4][daemon][NetProbe/NetProberSDK][][NetProberSDK init OK
[I][2025-04-03 +80 00:32:41.625][11862, 45][temporary-7][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.626][11862, 464][protocalManagerStatReport-464][daemon][HttpNetWorkTaskV2][][[310]callStart
[I][2025-04-03 +80 00:32:41.626][11862, 37][temporary-2][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.626][11862, 464][protocalManagerStatReport-464][daemon][HttpNetWorkTaskV2][][[310]dnsStart domainName:mayybstatnew.3g.qq.com
[W][2025-04-03 +80 00:32:41.627][11862, 37][temporary-2][daemon][jimxia][][jimxia, get appCaller: 1 get appVia:
[I][2025-04-03 +80 00:32:41.628][11862, 37][temporary-2][daemon][HttpNetWorkTaskV2][][[311]HttpNetWorkTaskV2 postUrl:http://mayybstatnew.3g.qq.com:80,seq:311,useHttp2:false
[I][2025-04-03 +80 00:32:41.629][11862, 35][temporary-1][daemon][LogTunnel][][processUAXXX 1
[I][2025-04-03 +80 00:32:41.638][11862, 462][protocalManagerStatReport-462][daemon][HttpNetWorkTaskV2][][[311]callStart
[I][2025-04-03 +80 00:32:41.639][11862, 462][protocalManagerStatReport-462][daemon][HttpNetWorkTaskV2][][[311]dnsStart domainName:mayybstatnew.3g.qq.com
[I][2025-04-03 +80 00:32:41.640][11862, 42][temporary-4][daemon][NetProbe/SettingsData][][SettingsData parse result:version:1.0
weakApSwitchTime:5000
weakSignalWaveTime:8000
weakSignalLevel:2
weakWaveSignalLevel:1
targets:[************, *********]
validPeriod:10
pingCount:10
pingInterval:400
deadLine:5
dnsTimeout:3000
weakLineWifi:15/200/1/50
weakLine4g:15/300/5/100
weakLine5g:15/200/1/50
supportIpv6:false
parseOK:true
[I][2025-04-03 +80 00:32:41.641][11862, 42][temporary-4][daemon][NetProbe/ProbeRecorder][][finish probe:
{busiId=1, hDomain=, hIp=, hIpSet=0, hType=0, isSyn=0, issueCate=, mainScene=, netType=ukn, pkgName=com.tencent.android.qqdownloader, probeCost=11, retCode=-201, retIsWeak=1, retType=3, sdkVer=1.1, settingVer=1.0, sigLevel=-1, sigWaveLevel=1, sigWeakLevel=2, startTime=1743611561629, subScene=, uid=}
[I][2025-04-03 +80 00:32:41.641][11862, 42][temporary-4][daemon][ProtocolReportUtilsNew][][finalReport param:{m_err_code=-828, m_net_weak_level=1, m_cmds=,2203,, m_http_code=-1, m_photon_cmd=, m_cost_time=8, m_report_ratio=10, m_ip=, m_exception=java.net.UnknownHostException: empty ip list! enableNac:false, enableHttpDns:true, m_photon_id=, m_rsp_size=-1, m_cs_load_succ=1, m_url=http://mayybnew.3g.qq.com:80, m_domain=mayybnew.3g.qq.com, m_net_probe_code=-201, m_is_keep_alive=0, m_trans_proto=2, m_retry_urls=-828: http://mayybnew.3g.qq.com:80, m_trans_cost=-1}
[W][2025-04-03 +80 00:32:42.006][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
[E][2025-04-03 +80 00:32:42.010][11862, 215][FloatingWindow-Refresh][daemon][WildToolbarNotification][][刷新频控，未到时间不准曝光
[I][2025-04-03 +80 00:32:42.043][11862, 457][protocalManagerStatReport-457][daemon][HttpDnsImpl][][domain:mayybstatnew.3g.qq.com lookupAll:[]
[I][2025-04-03 +80 00:32:42.045][11862, 457][protocalManagerStatReport-457][daemon][HttpNetWorkTaskV2][][[302]callFailed
[E][2025-04-03 +80 00:32:42.046][11862, 457][protocalManagerStatReport-457][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = StatReport
[E][2025-04-03 +80 00:32:42.046][11862, 457][protocalManagerStatReport-457][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = StatReport
[I][2025-04-03 +80 00:32:42.046][11862, 447][protocalManagerStatReport-447][daemon][HttpNetWorkTaskV2][][[309]callFailed
[E][2025-04-03 +80 00:32:42.047][11862, 447][protocalManagerStatReport-447][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = StatReport
[E][2025-04-03 +80 00:32:42.048][11862, 447][protocalManagerStatReport-447][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = StatReport
[I][2025-04-03 +80 00:32:42.048][11862, 463][protocalManagerStatReport-463][daemon][HttpNetWorkTaskV2][][[306]callFailed
[E][2025-04-03 +80 00:32:42.048][11862, 463][protocalManagerStatReport-463][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = StatReport
[E][2025-04-03 +80 00:32:42.049][11862, 463][protocalManagerStatReport-463][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = StatReport
[I][2025-04-03 +80 00:32:42.049][11862, 464][protocalManagerStatReport-464][daemon][HttpNetWorkTaskV2][][[310]callFailed
[E][2025-04-03 +80 00:32:42.050][11862, 464][protocalManagerStatReport-464][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = StatReport
[E][2025-04-03 +80 00:32:42.050][11862, 464][protocalManagerStatReport-464][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = StatReport
[I][2025-04-03 +80 00:32:42.051][11862, 462][protocalManagerStatReport-462][daemon][HttpNetWorkTaskV2][][[311]callFailed
[E][2025-04-03 +80 00:32:42.051][11862, 462][protocalManagerStatReport-462][daemon][AbsNetWorkTask][][traceId:0 msg:Exception:empty ip list! enableNac:false, enableHttpDns:true for cmdNames = StatReport
[E][2025-04-03 +80 00:32:42.052][11862, 462][protocalManagerStatReport-462][daemon][AbsNetWorkTask][][traceId:0 msg:onFinish error for errCode = -828 for cmdNames = StatReport
[I][2025-04-03 +80 00:54:06.313][11862, 26][Binder:11862_1][daemon][ionia_event_WallpaperVisibilityObserver][][onWallpaperVisibilityRealChanged, visible : true, displayId : 0
[I][2025-04-03 +80 00:54:06.316][11862, 26][Binder:11862_1][daemon][ionia_event_WallpaperVisibilityObserver][][onWallpaperVisibilityRealChanged, visible : false, displayId : 0
[E][2025-04-03 +80 00:54:06.335][11862, 75][alive_report][daemon][AliveFreezeCheckTask][][executeTask: report freeze. diffMs = 1275149 , thresholdRight = true , processFreezeCheckThreshold = 2.0 , currentDelayTimeMs = 10000 , thresholdMs = 20000.0 , duration = 1285149 , taskStartElapsedRealtimeMs = 463200231 , reportEventTime = 464485380
[I][2025-04-03 +80 00:54:06.373][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: duration = 1285149 , taskStartDate = 20250403 , currentDate = 20250403
[I][2025-04-03 +80 00:54:06.373][11862, 75][alive_report][daemon][AliveDurationUtils][][reportProcessFreeze: [20250403]-1285149 , durationMs = 1285149
[W][2025-04-03 +80 00:54:06.384][11862, 215][FloatingWindow-Refresh][daemon][CurrentRunningPkg][][no usage permission
~~~~~ end of mmap ~~~~~[31992,32030][2025-04-03 +0800 00:54:08]
^^^^^^^^^^Dec 12 2022^^^17:01:56^^^^^^^^^^[31992,32030][2025-04-03 +0800 00:54:08]
get mmap time: 2
MARS_URL: 
MARS_PATH: feature/xlog_minify
MARS_REVISION: fae9872
MARS_BUILD_TIME: 2022-12-12 17:01:21
MARS_BUILD_JOB: 
log appender mode:0, use mmap:1
cache dir space info, capacity:117430026240 free:77349617664 available:77215399936
log dir space info, capacity:117409054720 free:77194428416 available:77194428416
