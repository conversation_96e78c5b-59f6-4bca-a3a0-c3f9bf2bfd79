~~~~~ begin of mmap ~~~~~
[I][2025-04-02 +80 20:27:34.926][11819, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 20:29:18.901][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:29:18.920][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:29:18.925][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:29:18.927][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:29:18.931][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:30:35.493][11819, 1*][main][live][YYBLiveAuthService][][YYBLiveAuthService onDestroy
[E][2025-04-02 +80 20:30:35.494][11819, 1*][main][live][FLog_MainBinderManager][][onServiceDisconnected,processFlag:live
[I][2025-04-02 +80 20:30:35.500][11819, 41][temporary-4][live][MainBinderManager][][tryToConnect process:live
[E][2025-04-02 +80 20:30:35.509][11819, 41][temporary-4][live][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ebdd55d u0a3080 SVC  bg:+11s322ms idle change:idle procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:397)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922819.tf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 20:30:35.509][11819, 41][temporary-4][live][MainBinderManager][][tryToConnect process:live
[E][2025-04-02 +80 20:30:35.510][11819, 41][temporary-4][live][MainBinderManager][][tryToConnect failed
android.app.BackgroundServiceStartNotAllowedException: Not allowed to start service Intent { cmp=com.tencent.android.qqdownloader/com.tencent.assistant.main.MainService }: app is in background uid UidRecord{ebdd55d u0a3080 SVC  bg:+11s327ms idle change:idle procs:0 seq(0,0,0)}
	at android.app.ContextImpl.startServiceCommon(ContextImpl.java:2007)
	at android.app.ContextImpl.startService(ContextImpl.java:1963)
	at android.content.ContextWrapper.startService(ContextWrapper.java:774)
	at yyb8922819.e7.xc.x(ProGuard:415)
	at yyb8922819.e7.xc.w(ProGuard:407)
	at yyb8922819.e7.xc.j(ProGuard:398)
	at yyb8922819.e7.xc.b(Unknown Source:0)
	at yyb8922819.e7.xb.run(Unknown Source:2)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:462)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at yyb8922819.tf0.xi$xb$xb.run(ProGuard:61)
	at java.lang.Thread.run(Thread.java:933)
[I][2025-04-02 +80 20:57:10.541][11819, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 20:57:17.629][11819, 1*][main][live][NetworkInfoCache][][NetworkInfoCache markCacheNeedUpdate
[I][2025-04-02 +80 20:59:01.014][11819, 68][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:59:01.035][11819, 61][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:59:01.063][11819, 64][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:59:01.075][11819, 56][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[I][2025-04-02 +80 20:59:01.078][11819, 71][io_thread][live][RDelivery_DataManager_aa66b6582e_10001_][][reloadAllRDeliveryDatasFromDisc configCount = 322
[E][2025-04-02 +80 20:59:02.546][11819, 1*][main][live][HomeEventObserver][][action: android.intent.action.CLOSE_SYSTEM_DIALOGS,reason: recentapps
~~~~~ end of mmap ~~~~~[29232,29281][2025-04-02 +0800 23:42:47]
