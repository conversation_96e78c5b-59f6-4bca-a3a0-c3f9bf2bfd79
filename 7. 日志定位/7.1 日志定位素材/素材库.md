# 1. 请求到测试环境
- 定位日志：
[I][2025-04-03 +80 09:02:51.222][17196, 838][kuikly_thread-1][market][HTTPRequester][][|09:02.50.916|[KLog][HTTPRequester]:request start: HTTPReq(interfaceKeyword=activate_and_auth_for_h5, interfaceType=Access, baseURL=https://m-test.yyb.qq.com, url=/access/v3/activate_and_auth_for_h5, method=post, data={"app_id": 54375369,"scene_id": "230018","is_all_app": false,"is_only_query": true,"wait_time": 1000}, params={}, timeout=30000, reuse=false, reuseUniqueKey=, isTest=false, componentType=, componentID=, headers={content-type=application/json; charset=UTF-8, ual-access-requestid=moka-1743642170506, ual-access-businessid=yyb_h5, ual-access-timestamp=1743642170506, ual-access-nonce=57, ual-access-signature=3f95471158873627ccf4bbaf903b26cc, ual-access-login-appid=1101070898, ual-access-login-type=1, ual-access-openid=AF7D68EBEB4C29B38A44F4C5FF9F8D2F, ual-access-access-token=62D62F1D4CF99327B7E259EE698982B6})

- 原因 m-test 
baseURL=https://m-test.yyb.qq.com

# 2. 福利页用户激活之后 crash问题
kuikly debug模式开了，测试同学本地没有资源，然后就crash了

# 3. 用户反馈定时清理悬浮窗很快就消失，没有扫描中和清理的过程
- 定位日志：

- 原因
应用宝在前台，所以不展示定时清理浮窗+daemon进程被冻结


# 4. 用户反馈定时清理悬浮窗很快就消失，没有扫描中和清理的过程
shasionwu

crash
