现在有主进程日志文件和daemon日志文件。日志文件命名日下。

com.tencent.android.qqdownloader_2025040310.xlog.log
com.tencent.android.qqdownloader_2025040311.xlog.log
com.tencent.android.qqdownloader_2025040312.xlog.log


['com.tencent.android.qqdownloader_2025032615.xlog.log', 'com.tencent.android.qqdownloader_2025032614.xlog.log']


直接加字符串，找到这两个文件路径 检查有没有，合并
com.tencent.android.qqdownloader@daemon_2025032615.xlog.log

com.tencent.android.qqdownloader@daemon_2025032614.xlog.log

被动开启daemon日志分析，并需要提供tag


