6月1日~7月30日 调用日志工具的次数为 250次

- 日志分析（aisee平台）。近两周调用60次，供5人调用
- 日志分析（企微机器人）。近两周调用11次，供2人调用


平均两周企微机器人调用11次，aisee平台调用60次
60/14 + 11/14 = 5.075 次/天

输入token prompt平均8000字左右。用户日志的token不等，有些长有些短，暂记作一次40000token。48000，因此输入token为48000 * 5.075 = 243600 token
输出token 模型输出（思考+回答）。暂记作一次10000token。因此输出token为10000 * 5.075 = 50750 token


百万tokens输入单价为：4（元）
百万tokens输出单价为：16（元）
计算公式：输入单价 × 输入token + 输出单价 × 输出token

每天花费：
输入花费：243600/1000000 * 4 = 0.9744元
输出花费：50750/1000000 * 16 = 0.812元
合计：1.7864元

每月花费：1.7864 * 30 = 53.592元




版本工具

一、token计算

1. 场景识别
- prompt输入长度为：2000
- 输出token平均记作1000

2. 参数提取：
- prompt输入长度分别为：版本需求列表：1200；版本信息查询：7000；灰度数据分析：2000；日志分析：2000
  参数提取prompt长度总共为：1200+7000+2000+2000 = 12200
- 输出token平均记作1000

3. 版本灰度数据
- AI分析结论的prompt长度为：3000
- 输出token平均记作4000

二、花费计算

一次链路最多花费如下：
输入token为:2000+12200+3000 = 17200 token
输出token为：1000+1000+4000 = 6000 token

输入花费：17200/1000000 * 4 = 0.0688元
输出花费：6000/1000000 * 16 = 0.096元
合计一次花费：0.1648元

一天若记作30次调用（包括追问），则
一天花费：0.1648 * 30 = 4.944元
每月花费：4.944 * 30 = 148.32元


